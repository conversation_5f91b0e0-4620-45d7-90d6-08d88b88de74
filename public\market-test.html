<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>行情中心API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .pending { background: #fff3cd; border-color: #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .url {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px;
            border-radius: 3px;
        }
        .summary {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📈 行情中心API修复测试</h1>
        <div class="summary">
            <h3>🔧 修复内容</h3>
            <ul>
                <li>✅ 修复环境变量：VITE_API_BASE_URL 从 http://localhost:8000 改为 http://localhost:8000/api</li>
                <li>✅ 修复自选股API路径：/market/watchlist → /v1/market/watchlist</li>
                <li>✅ 统一所有API路径格式：/v1/module/endpoint</li>
                <li>✅ 确保所有请求都包含正确的 /api 前缀</li>
            </ul>
        </div>
        
        <button onclick="runAllTests()">🚀 运行所有测试</button>
        <button onclick="clearResults()">🗑️ 清空结果</button>
    </div>

    <div class="container">
        <h2>🔧 API端点测试</h2>
        
        <div id="test-watchlist" class="test-item pending">
            <h3>1. 自选股列表</h3>
            <div class="url">GET /api/v1/market/watchlist</div>
            <button onclick="testWatchlist()">测试</button>
            <div class="result" id="result-watchlist"></div>
        </div>

        <div id="test-news" class="test-item pending">
            <h3>2. 市场新闻</h3>
            <div class="url">GET /api/v1/market/news?limit=10</div>
            <button onclick="testNews()">测试</button>
            <div class="result" id="result-news"></div>
        </div>

        <div id="test-ranking-gainers" class="test-item pending">
            <h3>3. 涨幅榜</h3>
            <div class="url">GET /api/v1/market/overview/ranking?type=change_percent&limit=10</div>
            <button onclick="testRankingGainers()">测试</button>
            <div class="result" id="result-ranking-gainers"></div>
        </div>

        <div id="test-ranking-volume" class="test-item pending">
            <h3>4. 成交额榜</h3>
            <div class="url">GET /api/v1/market/overview/ranking?type=turnover&limit=10</div>
            <button onclick="testRankingVolume()">测试</button>
            <div class="result" id="result-ranking-volume"></div>
        </div>

        <div id="test-overview" class="test-item pending">
            <h3>5. 市场概览</h3>
            <div class="url">GET /api/v1/market/overview</div>
            <button onclick="testOverview()">测试</button>
            <div class="result" id="result-overview"></div>
        </div>

        <div id="test-stocks" class="test-item pending">
            <h3>6. 股票列表</h3>
            <div class="url">GET /api/v1/market/stocks?pageSize=5</div>
            <button onclick="testStocks()">测试</button>
            <div class="result" id="result-stocks"></div>
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8000';

        async function testAPI(url, testId, description) {
            const testElement = document.getElementById(`test-${testId}`);
            const resultElement = document.getElementById(`result-${testId}`);
            
            testElement.className = 'test-item pending';
            resultElement.innerHTML = '⏳ 测试中...';

            try {
                const startTime = Date.now();
                const response = await fetch(url);
                const endTime = Date.now();
                
                let data;
                try {
                    data = await response.json();
                } catch (e) {
                    data = await response.text();
                }

                if (response.ok) {
                    testElement.className = 'test-item success';
                    resultElement.innerHTML = `
                        <div style="color: green; font-weight: bold;">✅ 成功 (${endTime - startTime}ms)</div>
                        <div><strong>状态码:</strong> ${response.status}</div>
                        <div><strong>响应数据:</strong></div>
                        <pre>${typeof data === 'object' ? JSON.stringify(data, null, 2) : data}</pre>
                    `;
                    return data;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                testElement.className = 'test-item error';
                resultElement.innerHTML = `
                    <div style="color: red; font-weight: bold;">❌ 失败</div>
                    <div><strong>错误:</strong> ${error.message}</div>
                `;
                return null;
            }
        }

        async function testWatchlist() {
            await testAPI(`${BASE_URL}/api/v1/market/watchlist`, 'watchlist', '自选股列表');
        }

        async function testNews() {
            await testAPI(`${BASE_URL}/api/v1/market/news?limit=10`, 'news', '市场新闻');
        }

        async function testRankingGainers() {
            await testAPI(`${BASE_URL}/api/v1/market/overview/ranking?type=change_percent&limit=10`, 'ranking-gainers', '涨幅榜');
        }

        async function testRankingVolume() {
            await testAPI(`${BASE_URL}/api/v1/market/overview/ranking?type=turnover&limit=10`, 'ranking-volume', '成交额榜');
        }

        async function testOverview() {
            await testAPI(`${BASE_URL}/api/v1/market/overview`, 'overview', '市场概览');
        }

        async function testStocks() {
            await testAPI(`${BASE_URL}/api/v1/market/stocks?pageSize=5`, 'stocks', '股票列表');
        }

        async function runAllTests() {
            console.log('🚀 开始运行所有测试...');
            
            await testWatchlist();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testNews();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testRankingGainers();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testRankingVolume();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testOverview();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testStocks();
            
            console.log('✅ 所有测试完成');
        }

        function clearResults() {
            const testItems = document.querySelectorAll('.test-item');
            testItems.forEach(item => {
                item.className = 'test-item pending';
            });
            
            const results = document.querySelectorAll('.result');
            results.forEach(result => {
                result.innerHTML = '';
            });
        }

        // 页面加载完成后显示说明
        window.onload = function() {
            console.log('📋 行情中心API测试页面已加载');
            console.log('🔧 主要修复: 环境变量和API路径配置');
        };
    </script>
</body>
</html>
