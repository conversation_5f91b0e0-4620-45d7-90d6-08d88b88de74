<template>
  <div class="live-trading">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-info">
          <h1>
            <el-icon><Coin /></el-icon>
            实盘交易
          </h1>
          <p>基于MiniQMT的专业实盘交易终端</p>
        </div>
        
        <div class="header-actions">
          <el-tag 
            :type="connectionStatus === 'connected' ? 'success' : 'danger'" 
            size="large"
            class="connection-status"
          >
            <el-icon><Connection /></el-icon>
            {{ connectionStatus === 'connected' ? 'MiniQMT已连接' : 'MiniQMT未连接' }}
          </el-tag>
          
          <el-button 
            v-if="connectionStatus !== 'connected'"
            type="primary" 
            @click="connectToMiniQMT"
            :loading="connecting"
          >
            <el-icon><Link /></el-icon>
            连接MiniQMT
          </el-button>
          
          <el-button @click="showConfigDialog = true">
            <el-icon><Setting /></el-icon>
            交易配置
          </el-button>
          
          <el-button @click="refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 连接状态提示 -->
    <el-alert
      v-if="connectionStatus !== 'connected'"
      title="MiniQMT连接提示"
      type="warning"
      show-icon
      :closable="false"
      class="connection-alert"
    >
      <template #default>
        <div>
          <p>请确保MiniQMT客户端已启动并配置正确，然后点击"连接MiniQMT"按钮。</p>
          <ul>
            <li>确认MiniQMT客户端正在运行</li>
            <li>检查API接口配置（默认端口：58609）</li>
            <li>验证账户信息和交易权限</li>
          </ul>
        </div>
      </template>
    </el-alert>

    <!-- 账户信息 -->
    <div v-if="connectionStatus === 'connected'" class="account-info">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="info-card" shadow="hover">
            <div class="card-header">
              <span>账户资产</span>
              <el-icon class="card-icon"><Money /></el-icon>
            </div>
            <div class="card-value">
              <span class="amount">¥{{ formatMoney(accountData.totalAssets) }}</span>
              <span class="change positive">+{{ ((accountData.todayPnL / accountData.totalAssets) * 100).toFixed(2) }}%</span>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="info-card" shadow="hover">
            <div class="card-header">
              <span>可用资金</span>
              <el-icon class="card-icon"><Wallet /></el-icon>
            </div>
            <div class="card-value">
              <span class="amount">¥{{ formatMoney(accountData.availableCash) }}</span>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="info-card" shadow="hover">
            <div class="card-header">
              <span>持仓市值</span>
              <el-icon class="card-icon"><TrendCharts /></el-icon>
            </div>
            <div class="card-value">
              <span class="amount">¥{{ formatMoney(accountData.positionValue) }}</span>
            </div>
          </el-card>
        </el-col>
        
        <el-col :span="6">
          <el-card class="info-card" shadow="hover">
            <div class="card-header">
              <span>今日盈亏</span>
              <el-icon class="card-icon"><Trophy /></el-icon>
            </div>
            <div class="card-value">
              <span class="amount">¥{{ formatMoney(accountData.todayPnL) }}</span>
              <span :class="['change', accountData.todayPnL >= 0 ? 'positive' : 'negative']">
                {{ accountData.todayPnL >= 0 ? '+' : '' }}¥{{ Math.abs(accountData.todayPnL) }}
              </span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 主要交易区域 -->
    <div v-if="connectionStatus === 'connected'" class="trading-content">
      <el-row :gutter="20">
        <!-- 左侧：下单面板 -->
        <el-col :span="8">
          <el-card title="实盘下单" class="order-panel">
            <template #header>
              <div class="panel-header">
                <span>实盘下单</span>
                <el-button-group size="small">
                  <el-button 
                    :type="orderSide === 'buy' ? 'success' : 'default'"
                    @click="orderSide = 'buy'"
                  >
                    买入
                  </el-button>
                  <el-button 
                    :type="orderSide === 'sell' ? 'danger' : 'default'"
                    @click="orderSide = 'sell'"
                  >
                    卖出
                  </el-button>
                </el-button-group>
              </div>
            </template>

            <el-form :model="orderForm" label-width="80px" class="order-form">
              <el-form-item label="股票代码">
                <el-autocomplete
                  v-model="orderForm.symbol"
                  :fetch-suggestions="searchStocks"
                  placeholder="输入股票代码或名称"
                  style="width: 100%"
                  @select="handleStockSelect"
                >
                  <template #default="{ item }">
                    <div class="stock-suggestion">
                      <span class="stock-code">{{ item.symbol }}</span>
                      <span class="stock-name">{{ item.name }}</span>
                      <span class="stock-price">¥{{ item.price }}</span>
                    </div>
                  </template>
                </el-autocomplete>
              </el-form-item>

              <el-form-item label="订单类型">
                <el-select v-model="orderForm.orderType" style="width: 100%">
                  <el-option label="限价单" value="limit" />
                  <el-option label="市价单" value="market" />
                  <el-option label="条件单" value="conditional" />
                </el-select>
              </el-form-item>

              <el-form-item label="委托价格" v-if="orderForm.orderType !== 'market'">
                <el-input-number
                  v-model="orderForm.price"
                  :precision="2"
                  :step="0.01"
                  :min="0.01"
                  style="width: 100%"
                  placeholder="委托价格"
                />
              </el-form-item>

              <el-form-item label="委托数量">
                <el-input-number
                  v-model="orderForm.quantity"
                  :min="100"
                  :step="100"
                  style="width: 100%"
                  placeholder="委托数量"
                />
                <div class="quantity-shortcuts">
                  <el-button size="small" @click="setQuantity(100)">100</el-button>
                  <el-button size="small" @click="setQuantity(500)">500</el-button>
                  <el-button size="small" @click="setQuantity(1000)">1000</el-button>
                  <el-button size="small" @click="setMaxQuantity">全仓</el-button>
                </div>
              </el-form-item>

              <!-- 风险控制 -->
              <el-form-item>
                <el-collapse>
                  <el-collapse-item title="风险控制设置" name="risk">
                    <el-form-item label="止损价格">
                      <el-input-number
                        v-model="orderForm.stopLoss"
                        :precision="2"
                        :step="0.01"
                        style="width: 100%"
                        placeholder="止损价格"
                      />
                    </el-form-item>
                    
                    <el-form-item label="止盈价格">
                      <el-input-number
                        v-model="orderForm.takeProfit"
                        :precision="2"
                        :step="0.01"
                        style="width: 100%"
                        placeholder="止盈价格"
                      />
                    </el-form-item>
                  </el-collapse-item>
                </el-collapse>
              </el-form-item>

              <el-form-item>
                <div class="order-summary">
                  <div class="summary-item">
                    <span>预估金额：</span>
                    <span>¥{{ estimatedAmount }}</span>
                  </div>
                  <div class="summary-item">
                    <span>手续费：</span>
                    <span>¥{{ commission }}</span>
                  </div>
                  <div class="summary-item">
                    <span>印花税：</span>
                    <span>¥{{ stampTax }}</span>
                  </div>
                </div>
              </el-form-item>

              <el-form-item>
                <el-button 
                  :type="orderSide === 'buy' ? 'success' : 'danger'"
                  size="large"
                  style="width: 100%"
                  @click="confirmOrder"
                  :loading="submitting"
                  :disabled="!canSubmitOrder"
                >
                  {{ orderSide === 'buy' ? '买入' : '卖出' }}
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <!-- 中间：持仓和委托 -->
        <el-col :span="10">
          <el-card class="data-panel">
            <template #header>
              <el-tabs v-model="activeDataTab" class="data-tabs">
                <el-tab-pane label="持仓明细" name="positions" />
                <el-tab-pane label="委托订单" name="orders" />
                <el-tab-pane label="成交记录" name="trades" />
              </el-tabs>
            </template>

            <!-- 持仓明细 -->
            <div v-if="activeDataTab === 'positions'" class="positions-table">
              <el-table :data="positions" stripe style="width: 100%" max-height="400">
                <el-table-column prop="symbol" label="代码" width="80" />
                <el-table-column prop="name" label="名称" width="100" />
                <el-table-column prop="quantity" label="持仓" width="80" />
                <el-table-column prop="availableQty" label="可卖" width="80" />
                <el-table-column prop="avgCost" label="成本价" width="80">
                  <template #default="{ row }">
                    ¥{{ row.avgCost.toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column prop="currentPrice" label="现价" width="80">
                  <template #default="{ row }">
                    ¥{{ row.currentPrice.toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column prop="pnl" label="盈亏" width="100">
                  <template #default="{ row }">
                    <span :class="row.pnl >= 0 ? 'profit' : 'loss'">
                      {{ row.pnl >= 0 ? '+' : '' }}¥{{ row.pnl.toFixed(2) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="{ row }">
                    <el-button size="small" type="danger" @click="quickSell(row)">
                      卖出
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 委托订单 -->
            <div v-if="activeDataTab === 'orders'" class="orders-table">
              <el-table :data="orders" stripe style="width: 100%" max-height="400">
                <el-table-column prop="orderNo" label="订单号" width="100" />
                <el-table-column prop="symbol" label="代码" width="80" />
                <el-table-column prop="side" label="方向" width="60">
                  <template #default="{ row }">
                    <el-tag 
                      :type="row.side === 'buy' ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ row.side === 'buy' ? '买' : '卖' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="数量" width="80" />
                <el-table-column prop="price" label="委托价" width="80">
                  <template #default="{ row }">
                    ¥{{ row.price.toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="80">
                  <template #default="{ row }">
                    <el-tag :type="getOrderStatusType(row.status)" size="small">
                      {{ getOrderStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100">
                  <template #default="{ row }">
                    <el-button 
                      v-if="row.status === 'pending'"
                      size="small" 
                      type="warning" 
                      @click="cancelOrder(row)"
                    >
                      撤单
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 成交记录 -->
            <div v-if="activeDataTab === 'trades'" class="trades-table">
              <el-table :data="trades" stripe style="width: 100%" max-height="400">
                <el-table-column prop="tradeNo" label="成交号" width="100" />
                <el-table-column prop="symbol" label="代码" width="80" />
                <el-table-column prop="side" label="方向" width="60">
                  <template #default="{ row }">
                    <el-tag 
                      :type="row.side === 'buy' ? 'success' : 'danger'"
                      size="small"
                    >
                      {{ row.side === 'buy' ? '买' : '卖' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="数量" width="80" />
                <el-table-column prop="price" label="成交价" width="80">
                  <template #default="{ row }">
                    ¥{{ row.price.toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column prop="amount" label="成交额" width="100">
                  <template #default="{ row }">
                    ¥{{ row.amount.toFixed(2) }}
                  </template>
                </el-table-column>
                <el-table-column prop="tradeTime" label="成交时间" width="120">
                  <template #default="{ row }">
                    {{ formatDateTime(row.tradeTime) }}
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧：实时行情 -->
        <el-col :span="6">
          <el-card title="实时行情" class="market-panel">
            <div v-if="selectedStock" class="stock-detail">
              <div class="stock-header">
                <h3>{{ selectedStock.name }}</h3>
                <span class="stock-code">{{ selectedStock.symbol }}</span>
              </div>
              
              <div class="price-info">
                <div class="current-price">¥{{ selectedStock.price }}</div>
                <div :class="['price-change', selectedStock.change >= 0 ? 'positive' : 'negative']">
                  {{ selectedStock.change >= 0 ? '+' : '' }}{{ selectedStock.change }}
                  ({{ selectedStock.changePercent }}%)
                </div>
              </div>
              
              <div class="market-data">
                <div class="data-row">
                  <span>开盘:</span>
                  <span>¥{{ selectedStock.open }}</span>
                </div>
                <div class="data-row">
                  <span>最高:</span>
                  <span>¥{{ selectedStock.high }}</span>
                </div>
                <div class="data-row">
                  <span>最低:</span>
                  <span>¥{{ selectedStock.low }}</span>
                </div>
                <div class="data-row">
                  <span>成交量:</span>
                  <span>{{ formatVolume(selectedStock.volume) }}</span>
                </div>
              </div>
            </div>
            <div v-else class="no-selection">
              <el-empty description="请选择股票查看行情" />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- MiniQMT配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      title="MiniQMT配置"
      width="600px"
    >
      <el-form :model="qmtConfig" label-width="120px">
        <el-form-item label="服务器地址">
          <el-input v-model="qmtConfig.host" placeholder="localhost" />
        </el-form-item>
        
        <el-form-item label="API端口">
          <el-input-number v-model="qmtConfig.port" :min="1" :max="65535" style="width: 100%" />
        </el-form-item>
        
        <el-form-item label="账户编号">
          <el-input v-model="qmtConfig.account" placeholder="请输入交易账户编号" />
        </el-form-item>
        
        <el-form-item label="启用SSL">
          <el-switch v-model="qmtConfig.ssl" />
        </el-form-item>
        
        <el-form-item label="连接超时">
          <el-input-number v-model="qmtConfig.timeout" :min="1" :max="60" style="width: 100%" />
          <span style="margin-left: 8px; color: #909399;">秒</span>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showConfigDialog = false">取消</el-button>
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
      </template>
    </el-dialog>

    <!-- 订单确认对话框 -->
    <el-dialog
      v-model="showOrderConfirm"
      title="订单确认"
      width="400px"
    >
      <div class="order-confirm">
        <el-alert
          title="请仔细核对订单信息"
          type="warning"
          :closable="false"
          show-icon
        />
        
        <div class="confirm-details">
          <div class="detail-row">
            <span>股票：</span>
            <span>{{ orderForm.symbol }}</span>
          </div>
          <div class="detail-row">
            <span>方向：</span>
            <span :class="orderSide === 'buy' ? 'buy-text' : 'sell-text'">
              {{ orderSide === 'buy' ? '买入' : '卖出' }}
            </span>
          </div>
          <div class="detail-row">
            <span>数量：</span>
            <span>{{ orderForm.quantity }}股</span>
          </div>
          <div class="detail-row">
            <span>价格：</span>
            <span>¥{{ orderForm.price.toFixed(2) }}</span>
          </div>
          <div class="detail-row">
            <span>预估金额：</span>
            <span>¥{{ estimatedAmount }}</span>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showOrderConfirm = false">取消</el-button>
        <el-button 
          :type="orderSide === 'buy' ? 'success' : 'danger'" 
          @click="submitOrder"
          :loading="submitting"
        >
          确认{{ orderSide === 'buy' ? '买入' : '卖出' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Coin,
  Connection,
  Link,
  Setting,
  Refresh,
  Money,
  Wallet,
  TrendCharts,
  Trophy
} from '@element-plus/icons-vue'

// 响应式数据
const connectionStatus = ref<'connected' | 'disconnected' | 'connecting'>('disconnected')
const connecting = ref(false)
const showConfigDialog = ref(false)
const showOrderConfirm = ref(false)
const submitting = ref(false)
const activeDataTab = ref('positions')
const orderSide = ref<'buy' | 'sell'>('buy')
const selectedStock = ref<any>(null)

// MiniQMT配置
const qmtConfig = reactive({
  host: 'localhost',
  port: 58609,
  account: '',
  ssl: false,
  timeout: 10
})

// 账户数据
const accountData = reactive({
  totalAssets: 0,
  availableCash: 0,
  positionValue: 0,
  todayPnL: 0
})

// 下单表单
const orderForm = reactive({
  symbol: '',
  orderType: 'limit',
  price: 0,
  quantity: 100,
  stopLoss: 0,
  takeProfit: 0
})

// 持仓数据
const positions = ref<any[]>([])

// 订单数据
const orders = ref<any[]>([])

// 成交数据
const trades = ref<any[]>([])

// 计算属性
const estimatedAmount = computed(() => {
  const price = orderForm.orderType === 'market' ? (selectedStock.value?.price || 0) : orderForm.price
  return (price * orderForm.quantity).toFixed(2)
})

const commission = computed(() => {
  const amount = parseFloat(estimatedAmount.value)
  return Math.max(amount * 0.0003, 5).toFixed(2) // 万三手续费，最低5元
})

const stampTax = computed(() => {
  if (orderSide.value === 'sell') {
    const amount = parseFloat(estimatedAmount.value)
    return (amount * 0.001).toFixed(2) // 卖出时收取千分之一印花税
  }
  return '0.00'
})

const canSubmitOrder = computed(() => {
  return orderForm.symbol && orderForm.quantity > 0 && 
         (orderForm.orderType === 'market' || orderForm.price > 0)
})

// 方法
const formatMoney = (amount: number) => {
  return new Intl.NumberFormat('zh-CN').format(amount)
}

const formatVolume = (volume: number) => {
  if (volume >= *********) {
    return (volume / *********).toFixed(1) + '亿'
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(1) + '万'
  }
  return volume.toString()
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

const connectToMiniQMT = async () => {
  connecting.value = true
  connectionStatus.value = 'connecting'
  
  try {
    // 模拟连接MiniQMT
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 连接成功后加载数据
    connectionStatus.value = 'connected'
    await loadAccountData()
    await loadPositions()
    await loadOrders()
    await loadTrades()
    
    ElMessage.success('MiniQMT连接成功')
  } catch (error) {
    connectionStatus.value = 'disconnected'
    ElMessage.error('MiniQMT连接失败，请检查配置和网络')
  } finally {
    connecting.value = false
  }
}

const loadAccountData = async () => {
  // 模拟加载账户数据
  accountData.totalAssets = 1000000
  accountData.availableCash = 800000
  accountData.positionValue = 200000
  accountData.todayPnL = 5000
}

const loadPositions = async () => {
  // 模拟加载持仓数据
  positions.value = [
    {
      symbol: '000001',
      name: '平安银行',
      quantity: 1000,
      availableQty: 1000,
      avgCost: 12.00,
      currentPrice: 12.50,
      pnl: 500
    },
    {
      symbol: '000858',
      name: '五粮液',
      quantity: 200,
      availableQty: 200,
      avgCost: 175.00,
      currentPrice: 180.00,
      pnl: 1000
    }
  ]
}

const loadOrders = async () => {
  // 模拟加载订单数据
  orders.value = [
    {
      orderNo: 'QMT001',
      symbol: '000001',
      side: 'buy',
      quantity: 500,
      price: 12.30,
      status: 'pending'
    }
  ]
}

const loadTrades = async () => {
  // 模拟加载成交数据
  trades.value = [
    {
      tradeNo: 'T001',
      symbol: '000001',
      side: 'buy',
      quantity: 500,
      price: 12.25,
      amount: 6125,
      tradeTime: new Date()
    }
  ]
}

const searchStocks = async (queryString: string, callback: Function) => {
  // 模拟股票搜索
  const mockStocks = [
    { symbol: '000001', name: '平安银行', price: 12.50 },
    { symbol: '000002', name: '万科A', price: 20.80 },
    { symbol: '000858', name: '五粮液', price: 180.00 },
    { symbol: '600036', name: '招商银行', price: 42.50 },
    { symbol: '600519', name: '贵州茅台', price: 1680.00 }
  ]
  
  const results = queryString 
    ? mockStocks.filter(stock => 
        stock.symbol.includes(queryString) || 
        stock.name.includes(queryString)
      )
    : mockStocks.slice(0, 5)
  
  callback(results)
}

const handleStockSelect = (item: any) => {
  selectedStock.value = {
    ...item,
    open: item.price * 0.99,
    high: item.price * 1.02,
    low: item.price * 0.97,
    volume: Math.floor(Math.random() * 1000000) + 100000,
    change: (Math.random() - 0.5) * 2,
    changePercent: ((Math.random() - 0.5) * 4).toFixed(2)
  }
  orderForm.price = item.price
}

const setQuantity = (quantity: number) => {
  orderForm.quantity = quantity
}

const setMaxQuantity = () => {
  if (orderSide.value === 'buy') {
    const price = orderForm.orderType === 'market' ? 
      (selectedStock.value?.price || 0) : orderForm.price
    if (price > 0) {
      const maxQuantity = Math.floor(accountData.availableCash / price / 100) * 100
      orderForm.quantity = maxQuantity
    }
  } else {
    const position = positions.value.find(p => p.symbol === orderForm.symbol.split(' ')[0])
    if (position) {
      orderForm.quantity = position.availableQty
    }
  }
}

const confirmOrder = () => {
  if (!canSubmitOrder.value) {
    ElMessage.warning('请完善订单信息')
    return
  }
  
  showOrderConfirm.value = true
}

const submitOrder = async () => {
  submitting.value = true
  
  try {
    // 模拟提交订单到MiniQMT
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const newOrder = {
      orderNo: `QMT${Date.now()}`,
      symbol: orderForm.symbol.split(' ')[0],
      side: orderSide.value,
      quantity: orderForm.quantity,
      price: orderForm.price,
      status: 'pending'
    }
    
    orders.value.unshift(newOrder)
    
    ElMessage.success('订单提交成功')
    showOrderConfirm.value = false
    
    // 重置表单
    orderForm.symbol = ''
    orderForm.quantity = 100
    orderForm.price = 0
    selectedStock.value = null
    
  } catch (error) {
    ElMessage.error('订单提交失败')
  } finally {
    submitting.value = false
  }
}

const quickSell = (position: any) => {
  orderSide.value = 'sell'
  orderForm.symbol = `${position.symbol} ${position.name}`
  orderForm.quantity = position.availableQty
  orderForm.price = position.currentPrice
  
  handleStockSelect({
    symbol: position.symbol,
    name: position.name,
    price: position.currentPrice
  })
}

const cancelOrder = async (order: any) => {
  try {
    await ElMessageBox.confirm('确认撤销该订单？', '撤单确认', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟撤单
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const index = orders.value.findIndex(o => o.orderNo === order.orderNo)
    if (index !== -1) {
      orders.value[index].status = 'cancelled'
    }
    
    ElMessage.success('订单已撤销')
  } catch {
    // 用户取消
  }
}

const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    filled: 'success',
    cancelled: 'info',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待成交',
    filled: '已成交',
    cancelled: '已撤销',
    rejected: '已拒绝'
  }
  return statusMap[status] || '未知'
}

const saveConfig = () => {
  ElMessage.success('配置已保存')
  showConfigDialog.value = false
}

const refreshData = async () => {
  if (connectionStatus.value === 'connected') {
    await Promise.all([
      loadAccountData(),
      loadPositions(),
      loadOrders(),
      loadTrades()
    ])
    ElMessage.success('数据已刷新')
  }
}

onMounted(() => {
  // 尝试自动连接
  // connectToMiniQMT()
})

onUnmounted(() => {
  // 清理资源
})
</script>

<style scoped>
.live-trading {
  padding: 20px;
  min-height: 100vh;
  background: #f5f7fa;
}

.page-header {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info h1 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.header-info p {
  margin: 0;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.connection-alert {
  margin-bottom: 20px;
}

.connection-alert ul {
  margin: 8px 0 0 20px;
}

.account-info {
  margin-bottom: 20px;
}

.info-card {
  height: 120px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-icon {
  font-size: 24px;
  color: #409eff;
}

.card-value {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.amount {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.change {
  font-size: 14px;
}

.change.positive { color: #67c23a; }
.change.negative { color: #f56c6c; }

.trading-content {
  gap: 20px;
}

.order-panel,
.data-panel,
.market-panel {
  height: 650px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-form {
  max-height: 550px;
  overflow-y: auto;
}

.quantity-shortcuts {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.order-summary {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  margin-top: 16px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.data-tabs {
  margin: -10px 0 0 0;
}

.positions-table,
.orders-table,
.trades-table {
  height: 550px;
}

.profit { color: #67c23a; }
.loss { color: #f56c6c; }

.stock-detail {
  padding: 16px 0;
}

.stock-header {
  margin-bottom: 16px;
}

.stock-header h3 {
  margin: 0 0 4px 0;
  color: #303133;
}

.stock-code {
  color: #909399;
  font-size: 14px;
}

.price-info {
  margin-bottom: 20px;
}

.current-price {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.price-change {
  font-size: 16px;
  font-weight: 500;
}

.market-data {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
}

.data-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.data-row:last-child {
  margin-bottom: 0;
}

.no-selection {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.order-confirm {
  padding: 16px 0;
}

.confirm-details {
  margin-top: 16px;
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.buy-text { color: #67c23a; font-weight: 500; }
.sell-text { color: #f56c6c; font-weight: 500; }

.stock-suggestion {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.stock-code {
  font-weight: 500;
  color: #303133;
}

.stock-name {
  color: #909399;
  flex: 1;
  margin-left: 8px;
}

.stock-price {
  color: #303133;
  font-weight: 500;
}
</style>