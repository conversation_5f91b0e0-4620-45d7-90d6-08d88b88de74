/**
 * WebSocket统一配置
 */

const env = import.meta.env

// WebSocket基础配置
export const websocketConfig = {
  // 通用WebSocket连接 - 临时禁用以避免连接错误
  general: env.VITE_WS_URL || '', // 'ws://localhost:8000/api/v1/ws',

  // 市场数据WebSocket
  market: env.VITE_WS_MARKET_URL || '', // 'ws://localhost:8000/api/v1/ws/market',

  // 交易数据WebSocket
  trading: env.VITE_WS_TRADING_URL || '', // 'ws://localhost:8000/api/v1/ws/trading',

  // 策略监控WebSocket
  strategy: env.VITE_WS_STRATEGY_URL || '', // 'ws://localhost:8000/api/v1/ws/strategy',

  // 连接配置
  options: {
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    reconnectionAttempts: 5,
    timeout: 20000,
    transports: ['websocket', 'polling']
  }
}

// 获取WebSocket URL的辅助函数
export function getWebSocketUrl(type: 'general' | 'market' | 'trading' | 'strategy' = 'general'): string {
  return websocketConfig[type]
}

// 生产环境下自动使用wss协议
export function getSecureWebSocketUrl(url: string): string {
  if (env.PROD && url.startsWith('ws://')) {
    return url.replace('ws://', 'wss://')
  }
  return url
}
