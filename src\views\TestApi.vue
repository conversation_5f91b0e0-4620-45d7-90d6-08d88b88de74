<template>
  <div class="test-api">
    <h1>API测试页面</h1>
    <el-button @click="testApi">测试API</el-button>
    <pre>{{ apiResponse }}</pre>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { marketApi } from '@/api'

const apiResponse = ref('')

const testApi = async () => {
  try {
    console.log('开始测试API...')
    
    // 测试市场概览
    console.log('测试市场概览...')
    const overview = await marketApi.getMarketOverview()
    console.log('市场概览:', overview)
    
    // 测试股票列表
    console.log('测试股票列表...')
    const stocks = await marketApi.getStockList()
    console.log('股票列表:', stocks)
    
    // 测试板块数据
    console.log('测试板块数据...')
    const sectors = await marketApi.getSectors()
    console.log('板块数据:', sectors)
    
    // 测试排行榜
    console.log('测试排行榜...')
    const rankings = await marketApi.getRankings({ type: 'change_percent', limit: 10 })
    console.log('排行榜:', rankings)
    
    apiResponse.value = JSON.stringify({
      overview,
      stocks: stocks.slice(0, 5),
      sectors: sectors.slice(0, 5),
      rankings: rankings.slice(0, 5)
    }, null, 2)
  } catch (error) {
    console.error('API测试失败:', error)
    apiResponse.value = `错误: ${error}`
  }
}
</script>

<style scoped>
.test-api {
  padding: 20px;
}

pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  overflow: auto;
  max-height: 600px;
}
</style>