<template>
  <div class="backtest-results">
    <!-- 结果概览 -->
    <el-card class="results-overview" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><TrendCharts /></el-icon>
          <span>回测结果概览</span>
          <div class="header-actions">
            <el-button size="small" @click="exportResults">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
        </div>
      </template>

      <div class="overview-metrics">
        <div class="metric-row">
          <div class="metric-item">
            <div class="metric-label">总收益率</div>
            <div class="metric-value" :class="getReturnClass(results.totalReturn)">
              {{ formatPercent(results.totalReturn) }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">年化收益率</div>
            <div class="metric-value" :class="getReturnClass(results.annualReturn)">
              {{ formatPercent(results.annualReturn) }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">最大回撤</div>
            <div class="metric-value negative">
              {{ formatPercent(results.maxDrawdown) }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">夏普比率</div>
            <div class="metric-value">
              {{ results.sharpeRatio.toFixed(2) }}
            </div>
          </div>
        </div>

        <div class="metric-row">
          <div class="metric-item">
            <div class="metric-label">胜率</div>
            <div class="metric-value">
              {{ formatPercent(results.winRate) }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">盈亏比</div>
            <div class="metric-value">
              {{ results.profitLossRatio.toFixed(2) }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">交易次数</div>
            <div class="metric-value">
              {{ results.totalTrades }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">基准收益率</div>
            <div class="metric-value" :class="getReturnClass(results.benchmarkReturn)">
              {{ formatPercent(results.benchmarkReturn) }}
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 收益曲线图 -->
    <el-card class="results-chart" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><LineChart /></el-icon>
          <span>收益曲线</span>
        </div>
      </template>
      
      <div class="chart-container">
        <div ref="chartRef" class="chart" style="height: 400px;"></div>
      </div>
    </el-card>

    <!-- 详细统计 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="detailed-stats" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>详细统计</span>
            </div>
          </template>

          <el-descriptions :column="1" border>
            <el-descriptions-item label="回测开始时间">
              {{ formatDate(results.startDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="回测结束时间">
              {{ formatDate(results.endDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="回测天数">
              {{ results.totalDays }}天
            </el-descriptions-item>
            <el-descriptions-item label="初始资金">
              {{ formatCurrency(results.initialCapital) }}
            </el-descriptions-item>
            <el-descriptions-item label="最终资金">
              {{ formatCurrency(results.finalCapital) }}
            </el-descriptions-item>
            <el-descriptions-item label="最大资金">
              {{ formatCurrency(results.maxCapital) }}
            </el-descriptions-item>
            <el-descriptions-item label="最小资金">
              {{ formatCurrency(results.minCapital) }}
            </el-descriptions-item>
            <el-descriptions-item label="波动率">
              {{ formatPercent(results.volatility) }}
            </el-descriptions-item>
            <el-descriptions-item label="信息比率">
              {{ results.informationRatio.toFixed(2) }}
            </el-descriptions-item>
            <el-descriptions-item label="卡尔马比率">
              {{ results.calmarRatio.toFixed(2) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card class="trade-stats" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Operation /></el-icon>
              <span>交易统计</span>
            </div>
          </template>

          <el-descriptions :column="1" border>
            <el-descriptions-item label="总交易次数">
              {{ results.totalTrades }}
            </el-descriptions-item>
            <el-descriptions-item label="盈利交易次数">
              {{ results.profitTrades }}
            </el-descriptions-item>
            <el-descriptions-item label="亏损交易次数">
              {{ results.lossTrades }}
            </el-descriptions-item>
            <el-descriptions-item label="平均持仓天数">
              {{ results.avgHoldingDays.toFixed(1) }}天
            </el-descriptions-item>
            <el-descriptions-item label="平均盈利">
              {{ formatCurrency(results.avgProfit) }}
            </el-descriptions-item>
            <el-descriptions-item label="平均亏损">
              {{ formatCurrency(results.avgLoss) }}
            </el-descriptions-item>
            <el-descriptions-item label="最大单笔盈利">
              {{ formatCurrency(results.maxProfit) }}
            </el-descriptions-item>
            <el-descriptions-item label="最大单笔亏损">
              {{ formatCurrency(results.maxLoss) }}
            </el-descriptions-item>
            <el-descriptions-item label="手续费总计">
              {{ formatCurrency(results.totalCommission) }}
            </el-descriptions-item>
            <el-descriptions-item label="印花税总计">
              {{ formatCurrency(results.totalStampTax) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>

    <!-- 交易记录 -->
    <el-card class="trade-records" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><List /></el-icon>
          <span>交易记录</span>
          <div class="header-actions">
            <el-button size="small" @click="exportTrades">
              <el-icon><Download /></el-icon>
              导出交易记录
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="results.trades" stripe>
        <el-table-column prop="date" label="交易日期" width="120" />
        <el-table-column prop="symbol" label="股票代码" width="100" />
        <el-table-column prop="name" label="股票名称" width="120" />
        <el-table-column prop="action" label="操作" width="80">
          <template #default="{ row }">
            <el-tag :type="row.action === 'buy' ? 'success' : 'danger'">
              {{ row.action === 'buy' ? '买入' : '卖出' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="100" />
        <el-table-column prop="price" label="价格" width="100">
          <template #default="{ row }">
            ¥{{ row.price.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="120">
          <template #default="{ row }">
            ¥{{ row.amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="commission" label="手续费" width="100">
          <template #default="{ row }">
            ¥{{ row.commission.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="profit" label="盈亏" width="120">
          <template #default="{ row }">
            <span :class="row.profit >= 0 ? 'positive' : 'negative'">
              {{ row.profit >= 0 ? '+' : '' }}¥{{ row.profit.toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="交易原因" min-width="150" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  LineChart,
  DataAnalysis,
  Operation,
  List,
  Download
} from '@element-plus/icons-vue'

interface BacktestResults {
  // 基本信息
  startDate: string
  endDate: string
  totalDays: number
  initialCapital: number
  finalCapital: number
  maxCapital: number
  minCapital: number
  
  // 收益指标
  totalReturn: number
  annualReturn: number
  maxDrawdown: number
  volatility: number
  sharpeRatio: number
  informationRatio: number
  calmarRatio: number
  benchmarkReturn: number
  
  // 交易指标
  totalTrades: number
  profitTrades: number
  lossTrades: number
  winRate: number
  profitLossRatio: number
  avgHoldingDays: number
  avgProfit: number
  avgLoss: number
  maxProfit: number
  maxLoss: number
  totalCommission: number
  totalStampTax: number
  
  // 收益曲线数据
  equityCurve: Array<{ date: string; value: number; benchmark: number }>
  
  // 交易记录
  trades: Array<{
    date: string
    symbol: string
    name: string
    action: 'buy' | 'sell'
    quantity: number
    price: number
    amount: number
    commission: number
    profit: number
    reason: string
  }>
}

interface Props {
  results: BacktestResults
}

const props = defineProps<Props>()
const chartRef = ref()

// 格式化方法
const formatPercent = (value: number) => {
  return `${(value * 100).toFixed(2)}%`
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(value)
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const getReturnClass = (value: number) => {
  return value >= 0 ? 'positive' : 'negative'
}

// 导出功能
const exportResults = () => {
  ElMessage.success('导出功能开发中...')
}

const exportTrades = () => {
  ElMessage.success('导出交易记录功能开发中...')
}

// 初始化图表
const initChart = () => {
  // 这里可以使用 ECharts 或其他图表库
  // 暂时显示占位符
  if (chartRef.value) {
    chartRef.value.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">收益曲线图表（待实现）</div>'
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})
</script>

<style scoped>
.backtest-results {
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header span {
  margin-left: 8px;
  font-weight: 500;
}

.overview-metrics {
  padding: 20px 0;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.metric-row:last-child {
  margin-bottom: 0;
}

.metric-item {
  text-align: center;
  flex: 1;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.metric-value.positive {
  color: #f56c6c;
}

.metric-value.negative {
  color: #67c23a;
}

.chart-container {
  padding: 20px 0;
}

.chart {
  border: 1px solid #eee;
  border-radius: 4px;
}

.results-overview,
.results-chart,
.detailed-stats,
.trade-stats,
.trade-records {
  margin-bottom: 20px;
}

.positive {
  color: #f56c6c;
}

.negative {
  color: #67c23a;
}
</style>
