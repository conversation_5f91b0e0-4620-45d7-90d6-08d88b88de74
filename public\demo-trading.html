<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模拟交易账户演示</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .page-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .account-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .account-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .account-type-badge {
            display: inline-block;
            background: #ffd666;
            color: #d48806;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        .account-balance {
            font-size: 28px;
            font-weight: 600;
            color: #303133;
            margin: 10px 0;
        }
        .profit-positive {
            color: #f56c6c;
        }
        .profit-negative {
            color: #67c23a;
        }
        .account-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }
        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .detail-label {
            color: #606266;
            font-size: 14px;
        }
        .detail-value {
            font-weight: 500;
            color: #303133;
        }
        .positions-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .trading-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .simulation-notice {
            background: #fff4e6;
            border: 1px solid #ffd666;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            color: #d48806;
        }
        .simulation-notice h4 {
            margin: 0 0 10px 0;
            color: #d48806;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="demo-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1>🎯 量化交易平台 - 模拟账户演示</h1>
                <p>体验完整的模拟交易功能，包括账户管理、持仓跟踪和交易操作</p>
            </div>

            <!-- 模拟交易说明 -->
            <div class="simulation-notice">
                <h4>📋 模拟交易说明</h4>
                <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>这是一个完全模拟的交易环境，使用虚拟资金进行交易</li>
                    <li>所有数据均为模拟数据，不涉及真实资金交易</li>
                    <li>适合学习交易策略、熟悉平台操作和风险管理</li>
                    <li>可以安全地测试各种交易策略和参数设置</li>
                </ul>
            </div>

            <!-- 账户信息卡片 -->
            <div class="account-cards">
                <!-- 主账户信息 -->
                <div class="account-card">
                    <div class="account-type-badge">模拟账户</div>
                    <h3>账户总览</h3>
                    <div class="account-balance">¥{{ formatNumber(accountData.totalAssets) }}</div>
                    <p style="color: #606266; margin: 5px 0;">总资产</p>
                    
                    <div class="account-details">
                        <div class="detail-item">
                            <span class="detail-label">可用资金</span>
                            <span class="detail-value">¥{{ formatNumber(accountData.availableCash) }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">冻结资金</span>
                            <span class="detail-value">¥{{ formatNumber(accountData.frozenCash) }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">持仓市值</span>
                            <span class="detail-value">¥{{ formatNumber(accountData.marketValue) }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">仓位比例</span>
                            <span class="detail-value">{{ accountData.positionRatio }}%</span>
                        </div>
                    </div>
                </div>

                <!-- 盈亏信息 -->
                <div class="account-card">
                    <h3>盈亏统计</h3>
                    <div class="account-balance profit-positive">
                        +¥{{ formatNumber(accountData.totalProfit) }}
                    </div>
                    <p style="color: #606266; margin: 5px 0;">总盈亏 (+{{ accountData.totalProfitPercent }}%)</p>
                    
                    <div class="account-details">
                        <div class="detail-item">
                            <span class="detail-label">今日盈亏</span>
                            <span class="detail-value profit-positive">
                                +¥{{ formatNumber(accountData.dailyProfit) }}
                            </span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">今日盈亏率</span>
                            <span class="detail-value profit-positive">+{{ accountData.dailyProfitPercent }}%</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">最大回撤</span>
                            <span class="detail-value">-2.31%</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">胜率</span>
                            <span class="detail-value">73.5%</span>
                        </div>
                    </div>
                </div>

                <!-- 风险指标 -->
                <div class="account-card">
                    <h3>风险控制</h3>
                    <div style="margin: 15px 0;">
                        <div class="detail-item">
                            <span class="detail-label">风险等级</span>
                            <el-tag type="success" size="small">低风险</el-tag>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">保证金占用</span>
                            <span class="detail-value">15.2%</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">可开仓资金</span>
                            <span class="detail-value">¥{{ formatNumber(accountData.availableCash * 0.8) }}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">平仓线</span>
                            <span class="detail-value">¥{{ formatNumber(accountData.totalAssets * 0.3) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 持仓信息 -->
            <div class="positions-table">
                <el-table :data="positionsData" style="width: 100%" stripe>
                    <el-table-column prop="symbol" label="股票代码" width="100"></el-table-column>
                    <el-table-column prop="name" label="股票名称" width="120"></el-table-column>
                    <el-table-column prop="quantity" label="持仓数量" align="right">
                        <template #default="scope">
                            {{ formatNumber(scope.row.quantity) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="avgCost" label="持仓成本" align="right">
                        <template #default="scope">
                            ¥{{ scope.row.avgCost.toFixed(2) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="currentPrice" label="现价" align="right">
                        <template #default="scope">
                            ¥{{ scope.row.currentPrice.toFixed(2) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="marketValue" label="市值" align="right">
                        <template #default="scope">
                            ¥{{ formatNumber(scope.row.marketValue) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="unrealizedPnl" label="浮动盈亏" align="right">
                        <template #default="scope">
                            <span :class="scope.row.unrealizedPnl >= 0 ? 'profit-positive' : 'profit-negative'">
                                {{ scope.row.unrealizedPnl >= 0 ? '+' : '' }}¥{{ formatNumber(Math.abs(scope.row.unrealizedPnl)) }}
                            </span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="unrealizedPnlPercent" label="盈亏比例" align="right">
                        <template #default="scope">
                            <span :class="scope.row.unrealizedPnlPercent >= 0 ? 'profit-positive' : 'profit-negative'">
                                {{ scope.row.unrealizedPnlPercent >= 0 ? '+' : '' }}{{ scope.row.unrealizedPnlPercent.toFixed(2) }}%
                            </span>
                        </template>
                    </el-table-column>
                </el-table>
            </div>

            <!-- 操作按钮 -->
            <div class="trading-buttons">
                <el-button type="primary" @click="showTradeDialog = true">模拟下单</el-button>
                <el-button type="success" @click="refreshData">刷新数据</el-button>
                <el-button @click="showHelp">使用说明</el-button>
                <el-button @click="resetAccount">重置账户</el-button>
            </div>

            <!-- 模拟下单对话框 -->
            <el-dialog v-model="showTradeDialog" title="模拟下单" width="500px">
                <el-form :model="tradeForm" label-width="100px">
                    <el-form-item label="股票代码">
                        <el-input v-model="tradeForm.symbol" placeholder="输入股票代码"></el-input>
                    </el-form-item>
                    <el-form-item label="交易方向">
                        <el-radio-group v-model="tradeForm.side">
                            <el-radio label="buy">买入</el-radio>
                            <el-radio label="sell">卖出</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="委托数量">
                        <el-input-number v-model="tradeForm.quantity" :min="100" :step="100"></el-input-number>
                    </el-form-item>
                    <el-form-item label="委托价格">
                        <el-input-number v-model="tradeForm.price" :precision="2" :step="0.01"></el-input-number>
                    </el-form-item>
                </el-form>
                <template #footer>
                    <el-button @click="showTradeDialog = false">取消</el-button>
                    <el-button type="primary" @click="submitTrade">提交订单</el-button>
                </template>
            </el-dialog>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    showTradeDialog: false,
                    accountData: {
                        accountId: "SIMULATED-001",
                        accountType: "SIMULATED",
                        totalAssets: 1000000,
                        availableCash: 450000,
                        frozenCash: 50000,
                        marketValue: 500000,
                        totalProfit: 50000,
                        totalProfitPercent: 5.26,
                        dailyProfit: 2500,
                        dailyProfitPercent: 0.25,
                        positionRatio: 50.0
                    },
                    positionsData: [
                        {
                            symbol: "000001",
                            name: "平安银行",
                            quantity: 10000,
                            avgCost: 12.50,
                            currentPrice: 13.20,
                            marketValue: 132000,
                            unrealizedPnl: 7000,
                            unrealizedPnlPercent: 5.6
                        },
                        {
                            symbol: "000002",
                            name: "万科A",
                            quantity: 5000,
                            avgCost: 25.80,
                            currentPrice: 24.60,
                            marketValue: 123000,
                            unrealizedPnl: -6000,
                            unrealizedPnlPercent: -4.65
                        },
                        {
                            symbol: "000858",
                            name: "五粮液",
                            quantity: 1000,
                            avgCost: 165.20,
                            currentPrice: 168.50,
                            marketValue: 168500,
                            unrealizedPnl: 3300,
                            unrealizedPnlPercent: 2.0
                        },
                        {
                            symbol: "600036",
                            name: "招商银行",
                            quantity: 4000,
                            avgCost: 43.10,
                            currentPrice: 42.80,
                            marketValue: 171200,
                            unrealizedPnl: -1200,
                            unrealizedPnlPercent: -0.70
                        }
                    ],
                    tradeForm: {
                        symbol: '',
                        side: 'buy',
                        quantity: 100,
                        price: 0
                    }
                }
            },
            methods: {
                formatNumber(num) {
                    return new Intl.NumberFormat('zh-CN').format(num);
                },
                refreshData() {
                    // 模拟数据刷新
                    this.accountData.dailyProfit += Math.random() * 1000 - 500;
                    this.accountData.dailyProfitPercent = (this.accountData.dailyProfit / this.accountData.totalAssets * 100).toFixed(2);
                    
                    // 更新持仓价格
                    this.positionsData.forEach(position => {
                        const change = (Math.random() - 0.5) * 0.1;
                        position.currentPrice += change;
                        position.marketValue = position.quantity * position.currentPrice;
                        position.unrealizedPnl = position.marketValue - (position.quantity * position.avgCost);
                        position.unrealizedPnlPercent = (position.unrealizedPnl / (position.quantity * position.avgCost)) * 100;
                    });
                    
                    ElMessage.success('数据已刷新');
                },
                submitTrade() {
                    if (!this.tradeForm.symbol || !this.tradeForm.quantity || !this.tradeForm.price) {
                        ElMessage.warning('请填写完整的交易信息');
                        return;
                    }
                    
                    ElMessage.success(`模拟订单已提交：${this.tradeForm.side === 'buy' ? '买入' : '卖出'} ${this.tradeForm.symbol} ${this.tradeForm.quantity}股 @¥${this.tradeForm.price}`);
                    this.showTradeDialog = false;
                    
                    // 重置表单
                    this.tradeForm = {
                        symbol: '',
                        side: 'buy',
                        quantity: 100,
                        price: 0
                    };
                },
                showHelp() {
                    ElMessageBox.alert(
                        '这是量化交易平台的模拟交易演示。您可以：\n\n' +
                        '• 查看模拟账户的资金状况和持仓信息\n' +
                        '• 体验模拟下单功能\n' +
                        '• 观察持仓的实时盈亏变化\n' +
                        '• 学习风险控制和资金管理\n\n' +
                        '所有操作均为模拟，不涉及真实资金。',
                        '使用说明',
                        { confirmButtonText: '我知道了' }
                    );
                },
                resetAccount() {
                    ElMessageBox.confirm('确定要重置模拟账户吗？这将清除所有持仓和交易记录。', '确认重置', {
                        confirmButtonText: '确定重置',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // 重置账户数据
                        this.accountData = {
                            accountId: "SIMULATED-001",
                            accountType: "SIMULATED",
                            totalAssets: 1000000,
                            availableCash: 1000000,
                            frozenCash: 0,
                            marketValue: 0,
                            totalProfit: 0,
                            totalProfitPercent: 0,
                            dailyProfit: 0,
                            dailyProfitPercent: 0,
                            positionRatio: 0
                        };
                        this.positionsData = [];
                        ElMessage.success('模拟账户已重置');
                    }).catch(() => {
                        ElMessage.info('已取消重置');
                    });
                }
            },
            mounted() {
                ElMessage.info('欢迎使用量化交易平台模拟账户！');
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>