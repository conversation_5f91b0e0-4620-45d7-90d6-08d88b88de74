import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElButton } from 'element-plus'
import AppButton from '@/components/common/AppButton/index.vue'

// Mock Element Plus
vi.mock('element-plus', () => ({
  ElButton: {
    name: 'ElButton',
    template: '<button><slot /></button>',
    props: ['type', 'size', 'disabled', 'loading', 'icon']
  }
}))

describe('AppButton Component', () => {
  it('应该正确渲染基本按钮', () => {
    const wrapper = mount(AppButton, {
      props: {
        type: 'primary'
      },
      slots: {
        default: '提交'
      },
      global: {
        components: { ElButton }
      }
    })

    expect(wrapper.exists()).toBe(true)
    expect(wrapper.text()).toContain('提交')
  })

  it('应该传递正确的props到ElButton', () => {
    const wrapper = mount(AppButton, {
      props: {
        type: 'primary',
        size: 'large',
        disabled: true,
        loading: true
      },
      global: {
        components: { ElButton }
      }
    })

    const elButton = wrapper.findComponent(ElButton)
    expect(elButton.exists()).toBe(true)
    expect(elButton.props('type')).toBe('primary')
    expect(elButton.props('size')).toBe('large')
    expect(elButton.props('disabled')).toBe(true)
    expect(elButton.props('loading')).toBe(true)
  })

  it('应该正确处理点击事件', async () => {
    const clickHandler = vi.fn()
    const wrapper = mount(AppButton, {
      props: {
        onClick: clickHandler
      },
      global: {
        components: { ElButton }
      }
    })

    await wrapper.trigger('click')
    expect(clickHandler).toHaveBeenCalledTimes(1)
  })

  it('禁用状态下不应该触发点击事件', async () => {
    const clickHandler = vi.fn()
    const wrapper = mount(AppButton, {
      props: {
        disabled: true,
        onClick: clickHandler
      },
      global: {
        components: { ElButton }
      }
    })

    await wrapper.trigger('click')
    expect(clickHandler).not.toHaveBeenCalled()
  })

  it('加载状态下应该显示加载图标', () => {
    const wrapper = mount(AppButton, {
      props: {
        loading: true
      },
      global: {
        components: { ElButton }
      }
    })

    const elButton = wrapper.findComponent(ElButton)
    expect(elButton.props('loading')).toBe(true)
  })

  it('应该支持自定义图标', () => {
    const wrapper = mount(AppButton, {
      props: {
        icon: 'el-icon-search'
      },
      global: {
        components: { ElButton }
      }
    })

    const elButton = wrapper.findComponent(ElButton)
    expect(elButton.props('icon')).toBe('el-icon-search')
  })

  it('应该支持不同的按钮类型', () => {
    const types = ['primary', 'success', 'warning', 'danger', 'info', 'text']
    
    types.forEach(type => {
      const wrapper = mount(AppButton, {
        props: { type },
        global: {
          components: { ElButton }
        }
      })

      const elButton = wrapper.findComponent(ElButton)
      expect(elButton.props('type')).toBe(type)
    })
  })

  it('应该支持不同的按钮尺寸', () => {
    const sizes = ['large', 'default', 'small']
    
    sizes.forEach(size => {
      const wrapper = mount(AppButton, {
        props: { size },
        global: {
          components: { ElButton }
        }
      })

      const elButton = wrapper.findComponent(ElButton)
      expect(elButton.props('size')).toBe(size)
    })
  })
})