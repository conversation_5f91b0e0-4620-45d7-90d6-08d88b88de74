<template>
  <div class="simulated-trading">
    <!-- 顶部状态栏 -->
    <div class="trading-header">
      <div class="account-info">
        <div class="account-type">
          <span class="simulation-badge">模拟</span>
          <span class="account-name">模拟账户</span>
        </div>
        <div class="account-balance">
          <span class="label">总资产:</span>
          <span class="amount">¥{{ formatMoney(accountInfo.totalAssets) }}</span>
          <span class="label">可用:</span>
          <span class="amount available">¥{{ formatMoney(accountInfo.availableFunds) }}</span>
        </div>
      </div>
      <div class="market-status">
        <span class="status-indicator" :class="marketStatus.class">{{ marketStatus.text }}</span>
        <span class="current-time">{{ currentTime }}</span>
      </div>
    </div>

    <!-- 主要交易区域 -->
    <div class="trading-main">
      <!-- 左侧：股票搜索和五档行情 -->
      <div class="left-panel">
        <!-- 股票搜索 -->
        <div class="stock-search">
          <el-input
            v-model="stockCode"
            placeholder="输入股票代码或名称"
            @keyup.enter="searchStock"
            @input="onStockInput"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <div v-if="searchResults.length > 0" class="search-dropdown">
            <div
              v-for="stock in searchResults"
              :key="stock.code"
              class="search-item"
              @click="selectStock(stock)"
            >
              <span class="stock-code">{{ stock.code }}</span>
              <span class="stock-name">{{ stock.name }}</span>
              <span class="stock-price" :class="getPriceClass(stock.change)">
                {{ stock.price }}
              </span>
            </div>
          </div>
        </div>

        <!-- 当前股票信息 -->
        <div v-if="currentStock" class="current-stock">
          <div class="stock-header">
            <h3>{{ currentStock.code }} {{ currentStock.name }}</h3>
            <div class="stock-price-info">
              <span class="current-price" :class="getPriceClass(currentStock.change)">
                {{ currentStock.price }}
              </span>
              <span class="price-change" :class="getPriceClass(currentStock.change)">
                {{ currentStock.change > 0 ? '+' : '' }}{{ currentStock.change }}
                ({{ currentStock.changePercent }}%)
              </span>
            </div>
          </div>

          <!-- 五档行情 -->
          <div class="level-quotes">
            <div class="quotes-header">
              <span>五档行情</span>
              <span class="update-time">{{ currentStock.updateTime }}</span>
            </div>
            <div class="quotes-table">
              <div class="quotes-sell">
                <div v-for="(item, index) in currentStock.sellLevels" :key="'sell-' + index" class="quote-row sell">
                  <span class="level">卖{{ 5 - index }}</span>
                  <span class="price sell-price">{{ item.price }}</span>
                  <span class="volume">{{ item.volume }}</span>
                </div>
              </div>
              <div class="quotes-current">
                <div class="current-row">
                  <span class="current-label">现价</span>
                  <span class="current-price" :class="getPriceClass(currentStock.change)">
                    {{ currentStock.price }}
                  </span>
                  <span class="current-volume">{{ currentStock.volume }}</span>
                </div>
              </div>
              <div class="quotes-buy">
                <div v-for="(item, index) in currentStock.buyLevels" :key="'buy-' + index" class="quote-row buy">
                  <span class="level">买{{ index + 1 }}</span>
                  <span class="price buy-price">{{ item.price }}</span>
                  <span class="volume">{{ item.volume }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：K线图 -->
      <div class="center-panel">
        <div class="chart-container">
          <div class="chart-header">
            <div class="chart-tabs">
              <span
                v-for="period in chartPeriods"
                :key="period.value"
                class="chart-tab"
                :class="{ active: selectedPeriod === period.value }"
                @click="selectedPeriod = period.value"
              >
                {{ period.label }}
              </span>
            </div>
            <div class="chart-tools">
              <el-button size="small" @click="refreshChart">刷新</el-button>
              <el-button size="small" @click="toggleFullscreen">全屏</el-button>
            </div>
          </div>
          <div class="chart-content" ref="chartContainer">
            <!-- K线图将在这里渲染 -->
            <div v-if="!currentStock" class="chart-placeholder">
              请选择股票查看K线图
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：交易面板 -->
      <div class="right-panel">
        <!-- 买卖交易面板 -->
        <div class="trading-panel">
          <div class="panel-tabs">
            <div
              class="panel-tab"
              :class="{ active: activeTab === 'buy' }"
              @click="activeTab = 'buy'"
            >
              买入
            </div>
            <div
              class="panel-tab"
              :class="{ active: activeTab === 'sell' }"
              @click="activeTab = 'sell'"
            >
              卖出
            </div>
          </div>

          <div class="trading-form">
            <!-- 买入面板 -->
            <div v-if="activeTab === 'buy'" class="buy-panel">
              <div class="form-row">
                <label>股票代码:</label>
                <el-input v-model="tradeForm.stockCode" placeholder="股票代码" size="small" />
              </div>
              <div class="form-row">
                <label>买入价格:</label>
                <div class="price-input-group">
                  <el-input v-model="tradeForm.buyPrice" placeholder="价格" size="small" />
                  <div class="price-buttons">
                    <el-button size="small" @click="setBuyPrice('market')">市价</el-button>
                    <el-button size="small" @click="setBuyPrice('buy1')">买一</el-button>
                    <el-button size="small" @click="setBuyPrice('sell1')">卖一</el-button>
                  </div>
                </div>
              </div>
              <div class="form-row">
                <label>买入数量:</label>
                <div class="quantity-input-group">
                  <el-input v-model="tradeForm.buyQuantity" placeholder="股数" size="small" />
                  <div class="quantity-buttons">
                    <el-button size="small" @click="setQuantity(100)">100</el-button>
                    <el-button size="small" @click="setQuantity(500)">500</el-button>
                    <el-button size="small" @click="setQuantity(1000)">1000</el-button>
                    <el-button size="small" @click="setMaxQuantity">最大</el-button>
                  </div>
                </div>
              </div>
              <div class="form-row">
                <label>预估金额:</label>
                <span class="estimated-amount">¥{{ estimatedAmount }}</span>
              </div>
              <div class="form-actions">
                <el-button type="danger" size="large" @click="submitBuyOrder" :disabled="!canSubmitBuy">
                  买入下单
                </el-button>
              </div>
            </div>

            <!-- 卖出面板 -->
            <div v-if="activeTab === 'sell'" class="sell-panel">
              <div class="form-row">
                <label>股票代码:</label>
                <el-select v-model="tradeForm.sellStockCode" placeholder="选择持仓股票" size="small">
                  <el-option
                    v-for="position in positions"
                    :key="position.code"
                    :label="`${position.code} ${position.name}`"
                    :value="position.code"
                  />
                </el-select>
              </div>
              <div class="form-row">
                <label>卖出价格:</label>
                <div class="price-input-group">
                  <el-input v-model="tradeForm.sellPrice" placeholder="价格" size="small" />
                  <div class="price-buttons">
                    <el-button size="small" @click="setSellPrice('market')">市价</el-button>
                    <el-button size="small" @click="setSellPrice('buy1')">买一</el-button>
                    <el-button size="small" @click="setSellPrice('sell1')">卖一</el-button>
                  </div>
                </div>
              </div>
              <div class="form-row">
                <label>卖出数量:</label>
                <div class="quantity-input-group">
                  <el-input v-model="tradeForm.sellQuantity" placeholder="股数" size="small" />
                  <div class="quantity-buttons">
                    <el-button size="small" @click="setSellQuantity(100)">100</el-button>
                    <el-button size="small" @click="setSellQuantity('half')">一半</el-button>
                    <el-button size="small" @click="setSellQuantity('all')">全部</el-button>
                  </div>
                </div>
              </div>
              <div class="form-row">
                <label>可卖数量:</label>
                <span class="available-quantity">{{ availableSellQuantity }}股</span>
              </div>
              <div class="form-actions">
                <el-button type="success" size="large" @click="submitSellOrder" :disabled="!canSubmitSell">
                  卖出下单
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速交易按钮 -->
        <div class="quick-actions">
          <el-button type="primary" @click="showPositions">持仓查询</el-button>
          <el-button type="info" @click="showOrders">委托查询</el-button>
          <el-button type="warning" @click="showTrades">成交查询</el-button>
        </div>
      </div>
    </div>

    <!-- 底部：持仓和委托信息 -->
    <div class="trading-bottom">
      <el-tabs v-model="bottomActiveTab" class="bottom-tabs">
        <el-tab-pane label="持仓" name="positions">
          <div class="positions-table">
            <el-table :data="positions" size="small" stripe>
              <el-table-column prop="code" label="股票代码" width="100" />
              <el-table-column prop="name" label="股票名称" width="120" />
              <el-table-column prop="quantity" label="持仓数量" width="100" />
              <el-table-column prop="availableQuantity" label="可卖数量" width="100" />
              <el-table-column prop="avgPrice" label="成本价" width="100" />
              <el-table-column prop="currentPrice" label="现价" width="100" />
              <el-table-column prop="marketValue" label="市值" width="120" />
              <el-table-column prop="profitLoss" label="盈亏" width="120">
                <template #default="{ row }">
                  <span :class="getPriceClass(row.profitLoss)">
                    {{ row.profitLoss > 0 ? '+' : '' }}{{ formatMoney(row.profitLoss) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="150">
                <template #default="{ row }">
                  <el-button size="small" type="danger" @click="quickSell(row)">卖出</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="委托" name="orders">
          <div class="orders-table">
            <el-table :data="orders" size="small" stripe>
              <el-table-column prop="orderTime" label="委托时间" width="150" />
              <el-table-column prop="code" label="股票代码" width="100" />
              <el-table-column prop="name" label="股票名称" width="120" />
              <el-table-column prop="direction" label="买卖方向" width="80">
                <template #default="{ row }">
                  <span :class="row.direction === '买入' ? 'buy-text' : 'sell-text'">
                    {{ row.direction }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="委托价格" width="100" />
              <el-table-column prop="quantity" label="委托数量" width="100" />
              <el-table-column prop="dealQuantity" label="成交数量" width="100" />
              <el-table-column prop="status" label="状态" width="100" />
              <el-table-column label="操作" width="100">
                <template #default="{ row }">
                  <el-button
                    v-if="row.status === '未成交' || row.status === '部分成交'"
                    size="small"
                    type="warning"
                    @click="cancelOrder(row)"
                  >
                    撤单
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="成交" name="trades">
          <div class="trades-table">
            <el-table :data="trades" size="small" stripe>
              <el-table-column prop="tradeTime" label="成交时间" width="150" />
              <el-table-column prop="code" label="股票代码" width="100" />
              <el-table-column prop="name" label="股票名称" width="120" />
              <el-table-column prop="direction" label="买卖方向" width="80">
                <template #default="{ row }">
                  <span :class="row.direction === '买入' ? 'buy-text' : 'sell-text'">
                    {{ row.direction }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="成交价格" width="100" />
              <el-table-column prop="quantity" label="成交数量" width="100" />
              <el-table-column prop="amount" label="成交金额" width="120" />
              <el-table-column prop="fee" label="手续费" width="100" />
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

// 响应式数据
const stockCode = ref('')
const searchResults = ref([])
const currentStock = ref(null)
const selectedPeriod = ref('1d')
const activeTab = ref('buy')
const bottomActiveTab = ref('positions')
const currentTime = ref('')

// 账户信息
const accountInfo = reactive({
  totalAssets: 1000000, // 100万模拟资金
  availableFunds: 800000, // 80万可用资金
  marketValue: 200000, // 20万持仓市值
  profitLoss: 15000 // 1.5万盈亏
})

// 市场状态
const marketStatus = computed(() => {
  const now = new Date()
  const hour = now.getHours()
  const minute = now.getMinutes()
  const time = hour * 100 + minute

  if ((time >= 930 && time <= 1130) || (time >= 1300 && time <= 1500)) {
    return { text: '交易中', class: 'trading' }
  } else if (time >= 915 && time < 930) {
    return { text: '集合竞价', class: 'auction' }
  } else {
    return { text: '休市', class: 'closed' }
  }
})

// 交易表单
const tradeForm = reactive({
  stockCode: '',
  buyPrice: '',
  buyQuantity: '',
  sellStockCode: '',
  sellPrice: '',
  sellQuantity: ''
})

// K线图周期选项
const chartPeriods = [
  { label: '1分', value: '1m' },
  { label: '5分', value: '5m' },
  { label: '15分', value: '15m' },
  { label: '30分', value: '30m' },
  { label: '60分', value: '1h' },
  { label: '日K', value: '1d' },
  { label: '周K', value: '1w' },
  { label: '月K', value: '1M' }
]

// 持仓数据
const positions = ref([
  {
    code: '000001',
    name: '平安银行',
    quantity: 1000,
    availableQuantity: 1000,
    avgPrice: 12.50,
    currentPrice: 13.20,
    marketValue: 13200,
    profitLoss: 700
  },
  {
    code: '000002',
    name: '万科A',
    quantity: 500,
    availableQuantity: 500,
    avgPrice: 18.80,
    currentPrice: 17.90,
    marketValue: 8950,
    profitLoss: -450
  }
])

// 委托数据
const orders = ref([
  {
    orderTime: '2025-08-02 14:30:15',
    code: '000858',
    name: '五粮液',
    direction: '买入',
    price: 180.50,
    quantity: 100,
    dealQuantity: 0,
    status: '未成交'
  },
  {
    orderTime: '2025-08-02 14:25:30',
    code: '000001',
    name: '平安银行',
    direction: '买入',
    price: 13.20,
    quantity: 1000,
    dealQuantity: 1000,
    status: '已成交'
  }
])

// 成交数据
const trades = ref([
  {
    tradeTime: '2025-08-02 14:25:30',
    code: '000001',
    name: '平安银行',
    direction: '买入',
    price: 13.20,
    quantity: 1000,
    amount: 13200,
    fee: 6.6
  },
  {
    tradeTime: '2025-08-02 10:15:45',
    code: '000002',
    name: '万科A',
    direction: '买入',
    price: 18.80,
    quantity: 500,
    amount: 9400,
    fee: 4.7
  }
])

// 模拟股票数据
const mockStocks = [
  { code: '000001', name: '平安银行', price: 13.20, change: 0.15, changePercent: 1.15 },
  { code: '000002', name: '万科A', price: 17.90, change: -0.25, changePercent: -1.38 },
  { code: '000858', name: '五粮液', price: 180.50, change: 2.30, changePercent: 1.29 },
  { code: '600036', name: '招商银行', price: 42.80, change: 0.60, changePercent: 1.42 },
  { code: '600519', name: '贵州茅台', price: 1680.00, change: -15.50, changePercent: -0.91 }
]

// 计算属性
const estimatedAmount = computed(() => {
  const price = parseFloat(tradeForm.buyPrice) || 0
  const quantity = parseInt(tradeForm.buyQuantity) || 0
  return formatMoney(price * quantity)
})

const availableSellQuantity = computed(() => {
  const position = positions.value.find(p => p.code === tradeForm.sellStockCode)
  return position ? position.availableQuantity : 0
})

const canSubmitBuy = computed(() => {
  return tradeForm.stockCode && tradeForm.buyPrice && tradeForm.buyQuantity
})

const canSubmitSell = computed(() => {
  return tradeForm.sellStockCode && tradeForm.sellPrice && tradeForm.sellQuantity
})

// 方法
const formatMoney = (amount) => {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

const getPriceClass = (change) => {
  if (change > 0) return 'price-up'
  if (change < 0) return 'price-down'
  return 'price-flat'
}

const updateTime = () => {
  currentTime.value = new Date().toLocaleTimeString('zh-CN')
}

const onStockInput = (value) => {
  if (value.length >= 2) {
    searchResults.value = mockStocks.filter(stock =>
      stock.code.includes(value) || stock.name.includes(value)
    ).slice(0, 5)
  } else {
    searchResults.value = []
  }
}

const selectStock = (stock) => {
  stockCode.value = stock.code
  tradeForm.stockCode = stock.code
  searchResults.value = []

  // 模拟获取详细行情数据
  currentStock.value = {
    ...stock,
    updateTime: new Date().toLocaleTimeString('zh-CN'),
    volume: '1.2万手',
    sellLevels: [
      { price: (stock.price + 0.05).toFixed(2), volume: '120' },
      { price: (stock.price + 0.04).toFixed(2), volume: '89' },
      { price: (stock.price + 0.03).toFixed(2), volume: '156' },
      { price: (stock.price + 0.02).toFixed(2), volume: '203' },
      { price: (stock.price + 0.01).toFixed(2), volume: '178' }
    ],
    buyLevels: [
      { price: (stock.price - 0.01).toFixed(2), volume: '145' },
      { price: (stock.price - 0.02).toFixed(2), volume: '198' },
      { price: (stock.price - 0.03).toFixed(2), volume: '167' },
      { price: (stock.price - 0.04).toFixed(2), volume: '134' },
      { price: (stock.price - 0.05).toFixed(2), volume: '112' }
    ]
  }
}

const searchStock = () => {
  const stock = mockStocks.find(s => s.code === stockCode.value || s.name === stockCode.value)
  if (stock) {
    selectStock(stock)
  } else {
    ElMessage.warning('未找到该股票')
  }
}

const setBuyPrice = (type) => {
  if (!currentStock.value) return

  switch (type) {
    case 'market':
      tradeForm.buyPrice = currentStock.value.price
      break
    case 'buy1':
      tradeForm.buyPrice = currentStock.value.buyLevels[0].price
      break
    case 'sell1':
      tradeForm.buyPrice = currentStock.value.sellLevels[4].price
      break
  }
}

const setSellPrice = (type) => {
  if (!currentStock.value) return

  switch (type) {
    case 'market':
      tradeForm.sellPrice = currentStock.value.price
      break
    case 'buy1':
      tradeForm.sellPrice = currentStock.value.buyLevels[0].price
      break
    case 'sell1':
      tradeForm.sellPrice = currentStock.value.sellLevels[4].price
      break
  }
}

const setQuantity = (quantity) => {
  tradeForm.buyQuantity = quantity.toString()
}

const setMaxQuantity = () => {
  if (!tradeForm.buyPrice) return
  const price = parseFloat(tradeForm.buyPrice)
  const maxQuantity = Math.floor(accountInfo.availableFunds / price / 100) * 100
  tradeForm.buyQuantity = maxQuantity.toString()
}

const setSellQuantity = (type) => {
  const position = positions.value.find(p => p.code === tradeForm.sellStockCode)
  if (!position) return

  switch (type) {
    case 'half':
      tradeForm.sellQuantity = Math.floor(position.availableQuantity / 2 / 100) * 100
      break
    case 'all':
      tradeForm.sellQuantity = position.availableQuantity.toString()
      break
    default:
      if (typeof type === 'number') {
        tradeForm.sellQuantity = Math.min(type, position.availableQuantity).toString()
      }
  }
}

const submitBuyOrder = async () => {
  try {
    await ElMessageBox.confirm(
      `确认买入 ${tradeForm.stockCode} ${tradeForm.buyQuantity}股，价格 ${tradeForm.buyPrice}元？`,
      '确认下单',
      { type: 'warning' }
    )

    // 模拟下单
    const newOrder = {
      orderTime: new Date().toLocaleString('zh-CN'),
      code: tradeForm.stockCode,
      name: currentStock.value?.name || tradeForm.stockCode,
      direction: '买入',
      price: parseFloat(tradeForm.buyPrice),
      quantity: parseInt(tradeForm.buyQuantity),
      dealQuantity: 0,
      status: '未成交'
    }

    orders.value.unshift(newOrder)
    ElMessage.success('买入委托已提交')

    // 清空表单
    tradeForm.buyPrice = ''
    tradeForm.buyQuantity = ''

  } catch (error) {
    // 用户取消
  }
}

const submitSellOrder = async () => {
  try {
    await ElMessageBox.confirm(
      `确认卖出 ${tradeForm.sellStockCode} ${tradeForm.sellQuantity}股，价格 ${tradeForm.sellPrice}元？`,
      '确认下单',
      { type: 'warning' }
    )

    // 模拟下单
    const position = positions.value.find(p => p.code === tradeForm.sellStockCode)
    const newOrder = {
      orderTime: new Date().toLocaleString('zh-CN'),
      code: tradeForm.sellStockCode,
      name: position?.name || tradeForm.sellStockCode,
      direction: '卖出',
      price: parseFloat(tradeForm.sellPrice),
      quantity: parseInt(tradeForm.sellQuantity),
      dealQuantity: 0,
      status: '未成交'
    }

    orders.value.unshift(newOrder)
    ElMessage.success('卖出委托已提交')

    // 清空表单
    tradeForm.sellPrice = ''
    tradeForm.sellQuantity = ''

  } catch (error) {
    // 用户取消
  }
}

const cancelOrder = async (order) => {
  try {
    await ElMessageBox.confirm(`确认撤销委托？`, '确认撤单', { type: 'warning' })

    const index = orders.value.findIndex(o => o === order)
    if (index !== -1) {
      orders.value[index].status = '已撤单'
      ElMessage.success('撤单成功')
    }
  } catch (error) {
    // 用户取消
  }
}

const quickSell = (position) => {
  activeTab.value = 'sell'
  tradeForm.sellStockCode = position.code
  tradeForm.sellPrice = position.currentPrice.toString()
  tradeForm.sellQuantity = position.availableQuantity.toString()
}

const refreshChart = () => {
  ElMessage.info('图表已刷新')
}

const toggleFullscreen = () => {
  ElMessage.info('全屏功能开发中')
}

const showPositions = () => {
  bottomActiveTab.value = 'positions'
}

const showOrders = () => {
  bottomActiveTab.value = 'orders'
}

const showTrades = () => {
  bottomActiveTab.value = 'trades'
}

// 生命周期
onMounted(() => {
  updateTime()
  const timer = setInterval(updateTime, 1000)

  onUnmounted(() => {
    clearInterval(timer)
  })
})
</script>

<style scoped>
.simulated-trading {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 顶部状态栏 */
.trading-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #1e1e1e;
  color: white;
  padding: 8px 16px;
  font-size: 12px;
}

.account-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.account-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.simulation-badge {
  background: #ff6b35;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
}

.account-name {
  font-weight: bold;
}

.account-balance {
  display: flex;
  align-items: center;
  gap: 10px;
}

.label {
  color: #ccc;
}

.amount {
  color: #fff;
  font-weight: bold;
}

.amount.available {
  color: #67c23a;
}

.market-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-indicator {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: bold;
}

.status-indicator.trading {
  background: #67c23a;
  color: white;
}

.status-indicator.auction {
  background: #e6a23c;
  color: white;
}

.status-indicator.closed {
  background: #909399;
  color: white;
}

/* 主要交易区域 */
.trading-main {
  flex: 1;
  display: flex;
  gap: 1px;
  background: #ddd;
}

.left-panel {
  width: 280px;
  background: white;
  display: flex;
  flex-direction: column;
}

.center-panel {
  flex: 1;
  background: white;
  display: flex;
  flex-direction: column;
}

.right-panel {
  width: 300px;
  background: white;
  display: flex;
  flex-direction: column;
}

/* 股票搜索 */
.stock-search {
  position: relative;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.search-input {
  width: 100%;
}

.search-dropdown {
  position: absolute;
  top: 100%;
  left: 10px;
  right: 10px;
  background: white;
  border: 1px solid #ddd;
  border-top: none;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.search-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  border-bottom: 1px solid #f5f5f5;
}

.search-item:hover {
  background: #f5f5f5;
}

.stock-code {
  font-weight: bold;
  color: #333;
}

.stock-name {
  color: #666;
  flex: 1;
  margin-left: 8px;
}

.stock-price {
  font-weight: bold;
}

/* 当前股票信息 */
.current-stock {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.stock-header {
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.stock-header h3 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
}

.stock-price-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.current-price {
  font-size: 18px;
  font-weight: bold;
}

.price-change {
  font-size: 12px;
}

/* 五档行情 */
.level-quotes {
  flex: 1;
  padding: 10px;
}

.quotes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  font-size: 12px;
  color: #666;
}

.quotes-table {
  font-size: 11px;
}

.quote-row {
  display: grid;
  grid-template-columns: 30px 60px 50px;
  gap: 5px;
  padding: 2px 0;
  align-items: center;
}

.current-row {
  display: grid;
  grid-template-columns: 30px 60px 50px;
  gap: 5px;
  padding: 4px 0;
  align-items: center;
  background: #f5f5f5;
  margin: 2px 0;
}

.level {
  font-size: 10px;
  color: #666;
}

.price {
  font-weight: bold;
  text-align: right;
}

.sell-price {
  color: #67c23a;
}

.buy-price {
  color: #f56c6c;
}

.volume {
  text-align: right;
  color: #666;
  font-size: 10px;
}

/* K线图区域 */
.chart-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.chart-tabs {
  display: flex;
  gap: 5px;
}

.chart-tab {
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  border: 1px solid #ddd;
  background: #f5f5f5;
  border-radius: 3px;
}

.chart-tab.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.chart-content {
  flex: 1;
  position: relative;
  min-height: 400px;
}

.chart-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  font-size: 14px;
}

/* 交易面板 */
.trading-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.panel-tabs {
  display: flex;
  border-bottom: 1px solid #eee;
}

.panel-tab {
  flex: 1;
  padding: 10px;
  text-align: center;
  cursor: pointer;
  background: #f5f5f5;
  border-right: 1px solid #eee;
  font-size: 14px;
  font-weight: bold;
}

.panel-tab:last-child {
  border-right: none;
}

.panel-tab.active {
  background: white;
  color: #409eff;
}

.trading-form {
  flex: 1;
  padding: 15px;
}

.form-row {
  margin-bottom: 15px;
}

.form-row label {
  display: block;
  margin-bottom: 5px;
  font-size: 12px;
  color: #666;
  font-weight: bold;
}

.price-input-group,
.quantity-input-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.price-buttons,
.quantity-buttons {
  display: flex;
  gap: 5px;
}

.price-buttons .el-button,
.quantity-buttons .el-button {
  flex: 1;
  font-size: 10px;
  padding: 4px 8px;
}

.estimated-amount,
.available-quantity {
  font-weight: bold;
  color: #409eff;
}

.form-actions {
  margin-top: 20px;
}

.form-actions .el-button {
  width: 100%;
  font-size: 16px;
  font-weight: bold;
}

/* 快速操作 */
.quick-actions {
  padding: 15px;
  border-top: 1px solid #eee;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-actions .el-button {
  width: 100%;
}

/* 底部表格区域 */
.trading-bottom {
  height: 250px;
  background: white;
  border-top: 1px solid #ddd;
}

.bottom-tabs {
  height: 100%;
}

.bottom-tabs :deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow: auto;
}

.positions-table,
.orders-table,
.trades-table {
  height: 100%;
}

/* 价格颜色 */
.price-up {
  color: #f56c6c !important;
}

.price-down {
  color: #67c23a !important;
}

.price-flat {
  color: #333 !important;
}

.buy-text {
  color: #f56c6c;
  font-weight: bold;
}

.sell-text {
  color: #67c23a;
  font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .left-panel {
    width: 250px;
  }

  .right-panel {
    width: 280px;
  }
}

@media (max-width: 1000px) {
  .trading-main {
    flex-direction: column;
  }

  .left-panel,
  .right-panel {
    width: 100%;
    height: 300px;
  }

  .center-panel {
    height: 400px;
  }
}
</style>
