<!DOCTYPE html>
<html>
<head>
    <title>环境变量测试</title>
</head>
<body>
    <h1>环境变量测试</h1>
    <div id="env-info"></div>
    
    <script>
        // 测试API调用
        async function testApi() {
            try {
                console.log('Testing API...');

                // 测试市场概览
                console.log('Testing market overview...');
                const overviewResponse = await fetch('http://localhost:8000/api/v1/market/overview');
                const overviewData = await overviewResponse.json();
                console.log('Overview Response:', overviewData);

                // 测试股票列表
                console.log('Testing stock list...');
                const stocksResponse = await fetch('http://localhost:8000/api/v1/market/stocks?pageSize=3');
                const stocksData = await stocksResponse.json();
                console.log('Stocks Response:', stocksData);

                document.getElementById('env-info').innerHTML = `
                    <h2>API测试结果:</h2>
                    <h3>市场概览:</h3>
                    <pre>${JSON.stringify(overviewData, null, 2)}</pre>
                    <h3>股票列表:</h3>
                    <pre>${JSON.stringify(stocksData, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('API Error:', error);
                document.getElementById('env-info').innerHTML = `
                    <h2>API错误:</h2>
                    <pre>${error.message}</pre>
                `;
            }
        }

        testApi();
    </script>
</body>
</html>
