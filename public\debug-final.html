<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>滑块验证码测试 - 完整版</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
      background: #f5f7fa;
    }
    
    .test-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    h1 {
      color: #333;
      border-bottom: 2px solid #409eff;
      padding-bottom: 10px;
    }
    
    h2 {
      color: #666;
      margin-top: 0;
    }
    
    button {
      background: #409eff;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      margin: 5px;
      font-size: 14px;
      transition: all 0.3s ease;
    }
    
    button:hover {
      background: #66b1ff;
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0,0,0,0.2);
    }
    
    button:disabled {
      background: #ccc;
      cursor: not-allowed;
      transform: none;
    }
    
    .result {
      margin-top: 10px;
      padding: 10px;
      border-radius: 4px;
      background: #f4f4f5;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 12px;
      white-space: pre-wrap;
      max-height: 400px;
      overflow-y: auto;
    }
    
    .success {
      background: #f0f9ff;
      border: 1px solid #409eff;
      color: #409eff;
    }
    
    .error {
      background: #fef0f0;
      border: 1px solid #f56c6c;
      color: #f56c6c;
    }
    
    .captcha-container {
      margin: 20px 0;
      padding: 20px;
      background: #f9f9f9;
      border-radius: 8px;
      border: 1px solid #e4e7ed;
    }
    
    .slider-container {
      margin: 20px 0;
    }
    
    .slider-track {
      width: 100%;
      height: 40px;
      background: #e4e7ed;
      border-radius: 20px;
      position: relative;
      overflow: hidden;
    }
    
    .slider-thumb {
      width: 40px;
      height: 40px;
      background: #409eff;
      border-radius: 50%;
      position: absolute;
      top: 0;
      left: 0;
      cursor: grab;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
    }
    
    .slider-thumb:active {
      cursor: grabbing;
      transform: scale(1.1);
    }
    
    .status {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      margin-left: 10px;
    }
    
    .status.connected {
      background: #67c23a;
      color: white;
    }
    
    .status.disconnected {
      background: #f56c6c;
      color: white;
    }
    
    .log-entry {
      padding: 4px 0;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .log-time {
      color: #909399;
      font-size: 11px;
      margin-right: 8px;
    }
    
    .log-level {
      display: inline-block;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 11px;
      font-weight: 500;
      margin-right: 8px;
    }
    
    .log-level.info { background: #e6f7ff; color: #1890ff; }
    .log-level.success { background: #f0f9ff; color: #52c41a; }
    .log-level.warning { background: #fff7e6; color: #faad14; }
    .log-level.error { background: #fff1f0; color: #ff4d4f; }
    
    .test-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 10px;
      margin-top: 20px;
    }
    
    .test-card {
      background: #f5f7fa;
      padding: 15px;
      border-radius: 6px;
      text-align: center;
      transition: all 0.3s ease;
    }
    
    .test-card:hover {
      background: #e6f7ff;
      transform: translateY(-2px);
    }
    
    .api-endpoints {
      background: #f5f5f5;
      padding: 15px;
      border-radius: 6px;
      margin: 20px 0;
    }
    
    .endpoint {
      display: flex;
      align-items: center;
      margin: 8px 0;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 13px;
    }
    
    .method {
      display: inline-block;
      padding: 2px 8px;
      border-radius: 3px;
      font-weight: bold;
      margin-right: 10px;
      min-width: 50px;
      text-align: center;
    }
    
    .method.get { background: #61affe; color: white; }
    .method.post { background: #49cc90; color: white; }
    .method.ws { background: #fca130; color: white; }
  </style>
</head>
<body>
  <h1>🧪 滑块验证码测试 - 完整版</h1>
  
  <!-- API状态检查 -->
  <div class="test-section">
    <h2>📡 API状态检查</h2>
    <div class="api-endpoints">
      <div class="endpoint">
        <span class="method get">GET</span>
        <span>http://localhost:8002/api/captcha/slider</span>
      </div>
      <div class="endpoint">
        <span class="method get">GET</span>
        <span>http://localhost:8002/api/v1/auth/captcha/puzzle</span>
      </div>
      <div class="endpoint">
        <span class="method post">POST</span>
        <span>http://localhost:8002/api/captcha/slider/verify</span>
      </div>
      <div class="endpoint">
        <span class="method ws">WS</span>
        <span>ws://localhost:8002/ws</span>
      </div>
    </div>
    
    <button onclick="checkAPIStatus()">检查所有API状态</button>
    <button onclick="testProxy()">测试代理</button>
    <button onclick="testBackend()">直接测试后端</button>
    
    <div id="apiResult" class="result"></div>
  </div>

  <!-- 滑块验证码测试 -->
  <div class="test-section">
    <h2>🎯 滑块验证码测试</h2>
    
    <button onclick="getCaptcha()">获取验证码</button>
    <button onclick="getCaptchaViaProxy()">通过代理获取验证码</button>
    <button onclick="refreshCaptcha()">刷新验证码</button>
    
    <div id="captchaResult" class="result"></div>
    
    <div class="captcha-container" id="captchaContainer" style="display:none;">
      <h3>验证码预览</h3>
      <div id="captchaImage"></div>
      <div class="slider-container">
        <div class="slider-track">
          <div class="slider-thumb" id="sliderThumb">➜</div>
        </div>
      </div>
    </div>
  </div>

  <!-- WebSocket测试 -->
  <div class="test-section">
    <h2>🔌 WebSocket连接测试</h2>
    
    <button onclick="connectWebSocket()">连接WebSocket</button>
    <button onclick="disconnectWebSocket()">断开连接</button>
    <button onclick="sendWebSocketMessage()">发送测试消息</button>
    
    <span id="wsStatus" class="status disconnected">未连接</span>
    
    <div id="wsResult" class="result"></div>
  </div>

  <!-- 测试用例集合 -->
  <div class="test-section">
    <h2>🧪 快速测试用例</h2>
    
    <div class="test-grid">
      <div class="test-card">
        <h4>前端API测试</h4>
        <button onclick="testFrontendAPI()">运行测试</button>
      </div>
      <div class="test-card">
        <h4>完整流程测试</h4>
        <button onclick="testFullFlow()">运行测试</button>
      </div>
      <div class="test-card">
        <h4>性能测试</h4>
        <button onclick="testPerformance()">运行测试</button>
      </div>
      <div class="test-card">
        <h4>错误处理测试</h4>
        <button onclick="testErrorHandling()">运行测试</button>
      </div>
    </div>
    
    <div id="testResult" class="result" style="margin-top: 20px;"></div>
  </div>

  <!-- 日志输出 -->
  <div class="test-section">
    <h2>📋 实时日志</h2>
    <button onclick="clearLogs()">清空日志</button>
    <div id="logOutput" class="result" style="max-height: 300px;"></div>
  </div>

  <script>
    let ws = null;
    let captchaData = null;
    
    // 日志函数
    function log(message, level = 'info') {
      const logDiv = document.getElementById('logOutput');
      const time = new Date().toLocaleTimeString();
      const entry = document.createElement('div');
      entry.className = 'log-entry';
      entry.innerHTML = `
        <span class="log-time">${time}</span>
        <span class="log-level ${level}">${level.toUpperCase()}</span>
        <span>${message}</span>
      `;
      logDiv.appendChild(entry);
      logDiv.scrollTop = logDiv.scrollHeight;
    }
    
    function clearLogs() {
      document.getElementById('logOutput').innerHTML = '';
      log('日志已清空', 'info');
    }
    
    // API状态检查
    async function checkAPIStatus() {
      log('开始检查API状态...', 'info');
      const resultDiv = document.getElementById('apiResult');
      resultDiv.innerHTML = '检查中...';
      
      const apis = [
        { name: '后端根路径', url: 'http://localhost:8002/', method: 'GET' },
        { name: 'API文档', url: 'http://localhost:8002/docs', method: 'HEAD' },
        { name: '验证码接口 (直接)', url: 'http://localhost:8002/api/captcha/slider', method: 'GET' },
        { name: '验证码接口 (v1)', url: 'http://localhost:8002/api/v1/auth/captcha/puzzle', method: 'GET' },
        { name: '代理测试', url: '/api/captcha/slider', method: 'GET' }
      ];
      
      let results = [];
      
      for (const api of apis) {
        try {
          const response = await fetch(api.url, { method: api.method });
          const status = response.status;
          const success = status >= 200 && status < 300;
          
          results.push({
            name: api.name,
            url: api.url,
            status: status,
            success: success,
            message: success ? '正常' : `错误: ${status}`
          });
          
          log(`${api.name}: ${success ? '✅' : '❌'} ${status}`, success ? 'success' : 'error');
        } catch (error) {
          results.push({
            name: api.name,
            url: api.url,
            status: 'ERROR',
            success: false,
            message: error.message
          });
          
          log(`${api.name}: ❌ ${error.message}`, 'error');
        }
      }
      
      resultDiv.innerHTML = JSON.stringify(results, null, 2);
      resultDiv.className = results.every(r => r.success) ? 'result success' : 'result error';
    }
    
    // 测试代理
    async function testProxy() {
      log('测试代理连接...', 'info');
      const resultDiv = document.getElementById('apiResult');
      
      try {
        const response = await fetch('/api/captcha/slider');
        const data = await response.json();
        
        resultDiv.innerHTML = JSON.stringify(data, null, 2);
        resultDiv.className = 'result success';
        
        log('代理测试成功', 'success');
        
        if (data.success && data.data.background_image) {
          displayCaptcha(data.data);
        }
      } catch (error) {
        resultDiv.innerHTML = `错误: ${error.message}`;
        resultDiv.className = 'result error';
        log(`代理测试失败: ${error.message}`, 'error');
      }
    }
    
    // 直接测试后端
    async function testBackend() {
      log('直接测试后端...', 'info');
      const resultDiv = document.getElementById('apiResult');
      
      try {
        const response = await fetch('http://localhost:8002/api/captcha/slider');
        const data = await response.json();
        
        resultDiv.innerHTML = JSON.stringify(data, null, 2);
        resultDiv.className = 'result success';
        
        log('后端直接测试成功', 'success');
        
        if (data.success && data.data.background_image) {
          displayCaptcha(data.data);
        }
      } catch (error) {
        resultDiv.innerHTML = `错误: ${error.message}`;
        resultDiv.className = 'result error';
        log(`后端直接测试失败: ${error.message}`, 'error');
      }
    }
    
    // 获取验证码
    async function getCaptcha() {
      log('获取验证码...', 'info');
      const resultDiv = document.getElementById('captchaResult');
      
      try {
        const response = await fetch('http://localhost:8002/api/captcha/slider');
        const data = await response.json();
        
        captchaData = data.data;
        resultDiv.innerHTML = JSON.stringify(data, null, 2);
        resultDiv.className = 'result success';
        
        log('验证码获取成功', 'success');
        
        if (data.success && data.data.background_image) {
          displayCaptcha(data.data);
        }
      } catch (error) {
        resultDiv.innerHTML = `错误: ${error.message}`;
        resultDiv.className = 'result error';
        log(`获取验证码失败: ${error.message}`, 'error');
      }
    }
    
    // 通过代理获取验证码
    async function getCaptchaViaProxy() {
      log('通过代理获取验证码...', 'info');
      const resultDiv = document.getElementById('captchaResult');
      
      try {
        const response = await fetch('/api/v1/auth/captcha/puzzle');
        const data = await response.json();
        
        captchaData = data.data;
        resultDiv.innerHTML = JSON.stringify(data, null, 2);
        resultDiv.className = 'result success';
        
        log('通过代理获取验证码成功', 'success');
        
        if (data.success && data.data.background_image) {
          displayCaptcha(data.data);
        }
      } catch (error) {
        resultDiv.innerHTML = `错误: ${error.message}`;
        resultDiv.className = 'result error';
        log(`通过代理获取验证码失败: ${error.message}`, 'error');
      }
    }
    
    // 刷新验证码
    async function refreshCaptcha() {
      log('刷新验证码...', 'info');
      await getCaptcha();
    }
    
    // 显示验证码
    function displayCaptcha(data) {
      const container = document.getElementById('captchaContainer');
      const imageDiv = document.getElementById('captchaImage');
      
      container.style.display = 'block';
      
      // 确保图片有完整的data URL格式
      const backgroundImage = data.background_image.startsWith('data:') 
        ? data.background_image 
        : `data:image/png;base64,${data.background_image}`;
      
      const sliderImage = data.slider_image.startsWith('data:') 
        ? data.slider_image 
        : `data:image/png;base64,${data.slider_image}`;
      
      imageDiv.innerHTML = `
        <div style="position: relative; width: 400px; height: 200px; margin: 0 auto;">
          <img src="${backgroundImage}" style="width: 100%; height: 100%; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" />
          <img src="${sliderImage}" style="position: absolute; left: 0; top: ${data.y || 0}px; height: ${data.h || 80}px;" />
        </div>
        <p style="text-align: center; color: #666; margin-top: 10px;">
          目标位置: ${data.slider_x || 0}px, Y: ${data.slider_y || data.y || 0}px
        </p>
      `;
      
      log(`验证码显示成功 - ID: ${data.id}`, 'success');
    }
    
    // WebSocket测试
    function connectWebSocket() {
      log('连接WebSocket...', 'info');
      
      if (ws) {
        ws.close();
      }
      
      ws = new WebSocket('ws://localhost:8002/ws');
      
      ws.onopen = () => {
        document.getElementById('wsStatus').className = 'status connected';
        document.getElementById('wsStatus').textContent = '已连接';
        log('WebSocket连接成功', 'success');
        
        // 发送认证消息
        ws.send(JSON.stringify({
          type: 'auth',
          client_id: 'debug_client',
          token: null
        }));
      };
      
      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        document.getElementById('wsResult').innerHTML += `\n收到消息: ${JSON.stringify(data, null, 2)}\n`;
        log(`WebSocket消息: ${data.type}`, 'info');
      };
      
      ws.onerror = (error) => {
        log(`WebSocket错误: ${error}`, 'error');
      };
      
      ws.onclose = () => {
        document.getElementById('wsStatus').className = 'status disconnected';
        document.getElementById('wsStatus').textContent = '未连接';
        log('WebSocket连接关闭', 'warning');
      };
    }
    
    function disconnectWebSocket() {
      if (ws) {
        ws.close();
        ws = null;
        log('主动断开WebSocket连接', 'info');
      }
    }
    
    function sendWebSocketMessage() {
      if (ws && ws.readyState === WebSocket.OPEN) {
        const message = {
          type: 'ping',
          timestamp: Date.now()
        };
        ws.send(JSON.stringify(message));
        log('发送WebSocket ping消息', 'info');
      } else {
        log('WebSocket未连接', 'error');
      }
    }
    
    // 测试用例
    async function testFrontendAPI() {
      log('开始前端API测试...', 'info');
      const resultDiv = document.getElementById('testResult');
      
      const tests = [
        { name: '代理验证码API', url: '/api/captcha/slider' },
        { name: '代理v1验证码API', url: '/api/v1/auth/captcha/puzzle' }
      ];
      
      let passed = 0;
      let failed = 0;
      
      for (const test of tests) {
        try {
          const response = await fetch(test.url);
          const data = await response.json();
          
          if (data.success) {
            passed++;
            log(`✅ ${test.name} 测试通过`, 'success');
          } else {
            failed++;
            log(`❌ ${test.name} 测试失败`, 'error');
          }
        } catch (error) {
          failed++;
          log(`❌ ${test.name} 测试失败: ${error.message}`, 'error');
        }
      }
      
      resultDiv.innerHTML = `测试完成: ${passed} 通过, ${failed} 失败`;
      resultDiv.className = failed === 0 ? 'result success' : 'result error';
    }
    
    async function testFullFlow() {
      log('开始完整流程测试...', 'info');
      const resultDiv = document.getElementById('testResult');
      
      try {
        // 1. 获取验证码
        log('步骤1: 获取验证码', 'info');
        const captchaResponse = await fetch('/api/captcha/slider');
        const captchaData = await captchaResponse.json();
        
        if (!captchaData.success) {
          throw new Error('获取验证码失败');
        }
        
        log('✅ 验证码获取成功', 'success');
        
        // 2. 模拟验证
        log('步骤2: 模拟验证', 'info');
        const verifyResponse = await fetch('http://localhost:8002/api/captcha/slider/verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            id: captchaData.data.id,
            position: 100  // 模拟滑动位置
          })
        });
        
        const verifyData = await verifyResponse.json();
        log(`验证结果: ${verifyData.success ? '成功' : '失败'}`, verifyData.success ? 'success' : 'warning');
        
        resultDiv.innerHTML = '完整流程测试完成';
        resultDiv.className = 'result success';
        
      } catch (error) {
        resultDiv.innerHTML = `测试失败: ${error.message}`;
        resultDiv.className = 'result error';
        log(`测试失败: ${error.message}`, 'error');
      }
    }
    
    async function testPerformance() {
      log('开始性能测试...', 'info');
      const resultDiv = document.getElementById('testResult');
      
      const iterations = 10;
      const times = [];
      
      for (let i = 0; i < iterations; i++) {
        const start = performance.now();
        
        try {
          await fetch('/api/captcha/slider');
          const end = performance.now();
          const time = end - start;
          times.push(time);
          
          log(`请求 ${i + 1}: ${time.toFixed(2)}ms`, 'info');
        } catch (error) {
          log(`请求 ${i + 1} 失败: ${error.message}`, 'error');
        }
      }
      
      const avg = times.reduce((a, b) => a + b, 0) / times.length;
      const min = Math.min(...times);
      const max = Math.max(...times);
      
      resultDiv.innerHTML = `
性能测试结果 (${iterations} 次请求):
平均响应时间: ${avg.toFixed(2)}ms
最快响应时间: ${min.toFixed(2)}ms
最慢响应时间: ${max.toFixed(2)}ms
      `;
      resultDiv.className = 'result success';
    }
    
    async function testErrorHandling() {
      log('开始错误处理测试...', 'info');
      const resultDiv = document.getElementById('testResult');
      
      const tests = [
        {
          name: '无效的验证码ID',
          test: async () => {
            const response = await fetch('http://localhost:8002/api/captcha/slider/verify', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ id: 'invalid_id', position: 100 })
            });
            return await response.json();
          }
        },
        {
          name: '错误的请求格式',
          test: async () => {
            const response = await fetch('http://localhost:8002/api/captcha/slider/verify', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: 'invalid json'
            });
            return { status: response.status, ok: response.ok };
          }
        }
      ];
      
      let results = [];
      
      for (const test of tests) {
        try {
          const result = await test.test();
          results.push({ name: test.name, result: result });
          log(`${test.name}: 完成`, 'info');
        } catch (error) {
          results.push({ name: test.name, error: error.message });
          log(`${test.name}: 错误 - ${error.message}`, 'error');
        }
      }
      
      resultDiv.innerHTML = JSON.stringify(results, null, 2);
      resultDiv.className = 'result';
    }
    
    // 页面加载时自动执行
    window.onload = () => {
      log('调试页面已加载', 'info');
      log(`当前时间: ${new Date().toLocaleString()}`, 'info');
      log(`浏览器: ${navigator.userAgent}`, 'info');
    };
  </script>
</body>
</html>