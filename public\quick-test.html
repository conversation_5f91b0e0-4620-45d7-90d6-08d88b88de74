<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .market-buttons {
            display: flex;
            gap: 10px;
            margin: 10px 0;
        }
        .market-button {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: #f8f9fa;
            cursor: pointer;
            border-radius: 4px;
        }
        .market-button.active {
            background: #007bff;
            color: white;
        }
        select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .indices-display {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .index-card {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            text-align: center;
            min-width: 150px;
        }
        .index-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .index-value {
            font-size: 18px;
            margin-bottom: 5px;
        }
        .index-change {
            font-size: 14px;
        }
        .positive {
            color: #d32f2f;
        }
        .negative {
            color: #388e3c;
        }
    </style>
</head>
<body>
    <h1>市场页面快速测试</h1>
    
    <div class="section">
        <h2>大盘指数</h2>
        <button onclick="loadIndices()">加载指数数据</button>
        <div id="indices-container" class="indices-display"></div>
    </div>
    
    <div class="section">
        <h2>市场筛选按钮</h2>
        <div class="market-buttons">
            <div class="market-button active" onclick="selectMarket('all')">全部</div>
            <div class="market-button" onclick="selectMarket('sh')">沪A</div>
            <div class="market-button" onclick="selectMarket('sz')">深A</div>
            <div class="market-button" onclick="selectMarket('cyb')">创业板</div>
            <div class="market-button" onclick="selectMarket('kcb')">科创板</div>
        </div>
        <div>当前选择: <span id="selected-market">全部</span></div>
    </div>
    
    <div class="section">
        <h2>行业选择</h2>
        <button onclick="loadIndustries()">加载行业数据</button>
        <select id="industry-select">
            <option value="">选择行业</option>
        </select>
        <div id="industry-result" class="result"></div>
    </div>
    
    <div class="section">
        <h2>股票列表</h2>
        <button onclick="loadStocks()">加载股票数据</button>
        <div id="stocks-result" class="result"></div>
    </div>

    <script>
        // 使用相对路径，通过前端代理访问后端API
        const BASE_URL = '';
        let currentMarket = 'all';
        let stocksData = [];
        let industriesData = [];
        
        // 选择市场
        function selectMarket(market) {
            currentMarket = market;
            document.getElementById('selected-market').textContent = 
                market === 'all' ? '全部' :
                market === 'sh' ? '沪A' :
                market === 'sz' ? '深A' :
                market === 'cyb' ? '创业板' :
                market === 'kcb' ? '科创板' : market;
            
            // 更新按钮状态
            document.querySelectorAll('.market-button').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 重新筛选股票
            filterStocks();
        }
        
        // 加载指数数据
        async function loadIndices() {
            try {
                const response = await fetch(`${BASE_URL}/api/v1/market/overview`);
                const data = await response.json();
                
                if (data.data && data.data.indices) {
                    const container = document.getElementById('indices-container');
                    container.innerHTML = '';
                    
                    data.data.indices.forEach(index => {
                        const card = document.createElement('div');
                        card.className = 'index-card';
                        
                        const changeClass = index.changePercent > 0 ? 'positive' : 
                                          index.changePercent < 0 ? 'negative' : '';
                        
                        card.innerHTML = `
                            <div class="index-name">${index.name}</div>
                            <div class="index-value">${index.value}</div>
                            <div class="index-change ${changeClass}">
                                ${index.change > 0 ? '+' : ''}${index.change} 
                                (${index.changePercent > 0 ? '+' : ''}${index.changePercent}%)
                            </div>
                        `;
                        
                        container.appendChild(card);
                    });
                } else {
                    document.getElementById('indices-container').innerHTML = '❌ 无指数数据';
                }
            } catch (error) {
                document.getElementById('indices-container').innerHTML = `❌ 加载失败: ${error.message}`;
            }
        }
        
        // 加载行业数据
        async function loadIndustries() {
            const resultElement = document.getElementById('industry-result');
            const selectElement = document.getElementById('industry-select');
            
            try {
                resultElement.innerHTML = '⏳ 加载行业数据...';
                
                // 从股票数据中提取行业
                const stocksResponse = await fetch(`${BASE_URL}/api/v1/market/stocks?pageSize=100`);
                const stocksData = await stocksResponse.json();
                const stocks = stocksData.data?.items || [];
                
                // 从板块数据中获取行业
                const sectorsResponse = await fetch(`${BASE_URL}/api/v1/market/sectors`);
                const sectorsData = await sectorsResponse.json();
                const sectors = sectorsData.data || [];
                
                // 提取行业
                const industrySet = new Set();
                const industries = [];
                
                // 从股票中提取
                stocks.forEach(stock => {
                    if (stock.industry && !industrySet.has(stock.industry)) {
                        industrySet.add(stock.industry);
                        industries.push({
                            code: stock.industry,
                            name: stock.industry,
                            source: 'stocks'
                        });
                    }
                });
                
                // 从板块中提取
                sectors.forEach(sector => {
                    if (sector.name && !industrySet.has(sector.name)) {
                        industrySet.add(sector.name);
                        industries.push({
                            code: sector.name,
                            name: sector.name,
                            source: 'sectors'
                        });
                    }
                });
                
                industriesData = industries;
                
                // 更新下拉框
                selectElement.innerHTML = '<option value="">选择行业</option>';
                industries.forEach(industry => {
                    const option = document.createElement('option');
                    option.value = industry.code;
                    option.textContent = `${industry.name} (${industry.source})`;
                    selectElement.appendChild(option);
                });
                
                resultElement.innerHTML = `✅ 加载成功，共 ${industries.length} 个行业选项`;
                
            } catch (error) {
                resultElement.innerHTML = `❌ 加载失败: ${error.message}`;
            }
        }
        
        // 加载股票数据
        async function loadStocks() {
            const resultElement = document.getElementById('stocks-result');
            
            try {
                resultElement.innerHTML = '⏳ 加载股票数据...';
                
                const response = await fetch(`${BASE_URL}/api/v1/market/stocks?pageSize=20`);
                const data = await response.json();
                
                if (data.data && data.data.items) {
                    stocksData = data.data.items;
                    
                    let result = `✅ 加载成功，共 ${stocksData.length} 只股票\n\n`;
                    result += '前10只股票:\n';
                    
                    stocksData.slice(0, 10).forEach(stock => {
                        result += `${stock.symbol} ${stock.name} ${stock.currentPrice} `;
                        result += `${stock.change > 0 ? '+' : ''}${stock.change} `;
                        result += `(${stock.changePercent > 0 ? '+' : ''}${stock.changePercent}%) `;
                        result += `[${stock.industry}]\n`;
                    });
                    
                    resultElement.innerHTML = result;
                } else {
                    resultElement.innerHTML = '❌ 无股票数据';
                }
            } catch (error) {
                resultElement.innerHTML = `❌ 加载失败: ${error.message}`;
            }
        }
        
        // 筛选股票
        function filterStocks() {
            if (stocksData.length === 0) return;
            
            let filtered = stocksData;
            
            // 按市场筛选
            if (currentMarket !== 'all') {
                filtered = filtered.filter(stock => {
                    const symbol = stock.symbol;
                    switch (currentMarket) {
                        case 'sh':
                            return symbol.startsWith('6');
                        case 'sz':
                            return symbol.startsWith('0') && !symbol.startsWith('30');
                        case 'cyb':
                            return symbol.startsWith('30');
                        case 'kcb':
                            return symbol.startsWith('688');
                        default:
                            return true;
                    }
                });
            }
            
            const resultElement = document.getElementById('stocks-result');
            let result = `筛选结果 (${currentMarket}): ${filtered.length} 只股票\n\n`;
            
            filtered.slice(0, 10).forEach(stock => {
                result += `${stock.symbol} ${stock.name} ${stock.currentPrice} `;
                result += `${stock.change > 0 ? '+' : ''}${stock.change} `;
                result += `(${stock.changePercent > 0 ? '+' : ''}${stock.changePercent}%) `;
                result += `[${stock.industry}]\n`;
            });
            
            resultElement.innerHTML = result;
        }
        
        // 页面加载时自动测试
        window.onload = function() {
            console.log('快速测试页面加载完成');
            loadIndices();
            setTimeout(() => {
                loadIndustries();
                loadStocks();
            }, 1000);
        };
    </script>
</body>
</html>
