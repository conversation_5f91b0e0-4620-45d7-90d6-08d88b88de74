<template>
  <div class="test-captcha-page">
    <div class="container">
      <h1>🔒 滑动验证码测试页面</h1>
      
      <div class="test-info">
        <h3>测试说明</h3>
        <ul>
          <li>向右拖动滑块完成拼图验证</li>
          <li>验证成功会显示绿色提示</li>
          <li>验证失败会显示红色提示并自动重置</li>
          <li>点击右上角刷新按钮可以获取新的验证码</li>
        </ul>
      </div>
      
      <div class="captcha-container">
        <SliderCaptcha
          ref="captchaRef"
          @success="onSuccess"
          @fail="onFail"
          @refresh="onRefresh"
          :w="350"
          :h="180"
        />
      </div>
      
      <div v-if="message" class="result" :class="messageType">
        {{ message }}
      </div>
      
      <div class="controls">
        <el-button @click="refreshCaptcha" type="primary">
          <el-icon><Refresh /></el-icon>
          刷新验证码
        </el-button>
        <el-button @click="resetCaptcha" type="default">
          <el-icon><Close /></el-icon>
          重置状态
        </el-button>
        <el-button @click="goToLogin" type="success">
          <el-icon><User /></el-icon>
          前往登录页
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { Refresh, Close, User } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import SliderCaptcha from '@/components/common/SliderCaptcha/index.vue'

const router = useRouter()
const captchaRef = ref()
const message = ref('')
const messageType = ref('')

const onSuccess = () => {
  message.value = '✅ 验证成功！'
  messageType.value = 'success'
  ElMessage.success('滑动验证码验证通过！')
}

const onFail = () => {
  message.value = '❌ 验证失败，请重试'
  messageType.value = 'fail'
  ElMessage.error('验证失败，请重试')
}

const onRefresh = () => {
  message.value = ''
  messageType.value = ''
  ElMessage.info('验证码已刷新')
}

const refreshCaptcha = () => {
  captchaRef.value?.refresh()
}

const resetCaptcha = () => {
  captchaRef.value?.reset()
  message.value = ''
  messageType.value = ''
}

const goToLogin = () => {
  router.push('/login')
}
</script>

<style scoped>
.test-captcha-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.container {
  background: white;
  border-radius: 16px;
  padding: 40px;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 30px;
  font-size: 28px;
}

.test-info {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.test-info h3 {
  margin: 0 0 15px 0;
  color: #0369a1;
  font-size: 16px;
}

.test-info ul {
  margin: 0;
  padding-left: 20px;
  color: #0369a1;
}

.test-info li {
  margin-bottom: 8px;
  font-size: 14px;
}

.captcha-container {
  margin: 30px 0;
  display: flex;
  justify-content: center;
}

.result {
  text-align: center;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
  font-weight: 500;
  font-size: 16px;
}

.result.success {
  background: #f0f9ff;
  color: #059669;
  border: 1px solid #67c23a;
}

.result.fail {
  background: #fef0f0;
  color: #dc2626;
  border: 1px solid #f56c6c;
}

.controls {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.controls .el-button {
  display: flex;
  align-items: center;
  gap: 6px;
}

@media (max-width: 768px) {
  .container {
    padding: 20px;
    margin: 10px;
  }
  
  h1 {
    font-size: 24px;
  }
  
  .controls {
    flex-direction: column;
  }
  
  .controls .el-button {
    width: 100%;
    justify-content: center;
  }
}
</style>