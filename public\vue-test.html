<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue应用测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Vue应用测试</h1>
        
        <div id="vue-status" class="status info">检查Vue应用状态...</div>
        
        <div id="app">
            <div style="padding: 20px; text-align: center;">
                <h2>{{ title }}</h2>
                <p>{{ message }}</p>
                <p>计数器: {{ count }}</p>
                <button @click="increment">点击 +1</button>
                <button @click="testAPI">测试API</button>
                <div v-if="apiResult" style="margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                    <h4>API测试结果:</h4>
                    <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        console.log('🔧 开始Vue测试...')
        
        // 检查Vue是否可用
        try {
            const { createApp } = await import('vue')
            console.log('✅ Vue导入成功')
            
            document.getElementById('vue-status').innerHTML = '✅ Vue导入成功'
            document.getElementById('vue-status').className = 'status success'
            
            const app = createApp({
                data() {
                    return {
                        title: '🎉 Vue 3 应用正常运行！',
                        message: '前端框架工作正常',
                        count: 0,
                        apiResult: null
                    }
                },
                async mounted() {
                    console.log('✅ Vue应用已挂载')
                    document.title = 'Vue测试 - 运行正常'
                },
                methods: {
                    increment() {
                        this.count++
                        console.log(`计数器: ${this.count}`)
                    },
                    async testAPI() {
                        try {
                            console.log('🔄 测试API连接...')
                            const response = await fetch('http://localhost:8000/api/health')
                            const data = await response.json()
                            this.apiResult = {
                                status: 'success',
                                data: data,
                                timestamp: new Date().toISOString()
                            }
                            console.log('✅ API测试成功:', data)
                        } catch (error) {
                            this.apiResult = {
                                status: 'error',
                                error: error.message,
                                timestamp: new Date().toISOString()
                            }
                            console.error('❌ API测试失败:', error)
                        }
                    }
                }
            })
            
            console.log('✅ Vue应用创建成功')
            app.mount('#app')
            console.log('🚀 Vue应用挂载完成！')
            
        } catch (error) {
            console.error('❌ Vue应用加载失败:', error)
            document.getElementById('vue-status').innerHTML = `❌ Vue加载失败: ${error.message}`
            document.getElementById('vue-status').className = 'status error'
            
            document.getElementById('app').innerHTML = `
                <div style="padding: 20px; text-align: center; color: red;">
                    <h2>❌ Vue应用加载失败</h2>
                    <p>错误: ${error.message}</p>
                    <p>请检查控制台获取详细信息</p>
                </div>
            `
        }
    </script>
</body>
</html>
