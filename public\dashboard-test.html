<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表盘API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .pending { background: #fff3cd; border-color: #ffeaa7; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .url {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px;
            border-radius: 3px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }
        .metric-label {
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 仪表盘API修复测试</h1>
        <p>测试仪表盘相关的API端点是否正常工作</p>
        
        <button onclick="runAllTests()">🚀 运行所有测试</button>
        <button onclick="clearResults()">🗑️ 清空结果</button>
    </div>

    <div class="container">
        <h2>🔧 API端点测试</h2>
        
        <div id="test-account" class="test-item pending">
            <h3>1. 交易账户信息</h3>
            <div class="url">GET /api/v1/trading/account</div>
            <button onclick="testAccount()">测试</button>
            <div class="result" id="result-account"></div>
        </div>

        <div id="test-positions" class="test-item pending">
            <h3>2. 持仓信息</h3>
            <div class="url">GET /api/v1/trading/positions</div>
            <button onclick="testPositions()">测试</button>
            <div class="result" id="result-positions"></div>
        </div>

        <div id="test-market-overview" class="test-item pending">
            <h3>3. 市场概览</h3>
            <div class="url">GET /api/v1/market/overview</div>
            <button onclick="testMarketOverview()">测试</button>
            <div class="result" id="result-market-overview"></div>
        </div>

        <div id="test-stocks" class="test-item pending">
            <h3>4. 股票列表</h3>
            <div class="url">GET /api/v1/market/stocks</div>
            <button onclick="testStocks()">测试</button>
            <div class="result" id="result-stocks"></div>
        </div>
    </div>

    <div class="container">
        <h2>📈 仪表盘数据展示</h2>
        <div id="dashboard-data" class="metrics-grid">
            <!-- 动态生成的指标卡片 -->
        </div>
    </div>

    <script>
        const BASE_URL = 'http://localhost:8000';

        async function testAPI(url, testId, description) {
            const testElement = document.getElementById(`test-${testId}`);
            const resultElement = document.getElementById(`result-${testId}`);
            
            testElement.className = 'test-item pending';
            resultElement.innerHTML = '⏳ 测试中...';

            try {
                const startTime = Date.now();
                const response = await fetch(url);
                const endTime = Date.now();
                const data = await response.json();

                if (response.ok) {
                    testElement.className = 'test-item success';
                    resultElement.innerHTML = `
                        <div style="color: green; font-weight: bold;">✅ 成功 (${endTime - startTime}ms)</div>
                        <div><strong>状态码:</strong> ${response.status}</div>
                        <div><strong>响应数据:</strong></div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    return data;
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                testElement.className = 'test-item error';
                resultElement.innerHTML = `
                    <div style="color: red; font-weight: bold;">❌ 失败</div>
                    <div><strong>错误:</strong> ${error.message}</div>
                `;
                return null;
            }
        }

        async function testAccount() {
            const data = await testAPI(`${BASE_URL}/api/v1/trading/account`, 'account', '交易账户');
            if (data && data.success) {
                updateDashboard('account', data.data);
            }
        }

        async function testPositions() {
            await testAPI(`${BASE_URL}/api/v1/trading/positions`, 'positions', '持仓信息');
        }

        async function testMarketOverview() {
            const data = await testAPI(`${BASE_URL}/api/v1/market/overview`, 'market-overview', '市场概览');
            if (data && data.success) {
                updateDashboard('market', data.data);
            }
        }

        async function testStocks() {
            await testAPI(`${BASE_URL}/api/v1/market/stocks?pageSize=5`, 'stocks', '股票列表');
        }

        function updateDashboard(type, data) {
            const dashboardElement = document.getElementById('dashboard-data');
            
            if (type === 'account') {
                const accountCards = `
                    <div class="metric-card">
                        <div class="metric-value">¥${(data.totalAssets || 0).toLocaleString()}</div>
                        <div class="metric-label">总资产</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">¥${(data.availableCash || 0).toLocaleString()}</div>
                        <div class="metric-label">可用资金</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">¥${(data.marketValue || 0).toLocaleString()}</div>
                        <div class="metric-label">持仓市值</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" style="color: ${(data.totalProfit || 0) >= 0 ? '#52c41a' : '#ff4d4f'}">
                            ¥${(data.totalProfit || 0).toLocaleString()}
                        </div>
                        <div class="metric-label">总盈亏</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value" style="color: ${(data.dailyProfit || 0) >= 0 ? '#52c41a' : '#ff4d4f'}">
                            ¥${(data.dailyProfit || 0).toLocaleString()}
                        </div>
                        <div class="metric-label">今日盈亏</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${(data.positionRatio || 0).toFixed(1)}%</div>
                        <div class="metric-label">仓位比例</div>
                    </div>
                `;
                dashboardElement.innerHTML = accountCards;
            }
        }

        async function runAllTests() {
            console.log('🚀 开始运行所有测试...');
            
            await testAccount();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testPositions();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testMarketOverview();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testStocks();
            
            console.log('✅ 所有测试完成');
        }

        function clearResults() {
            const testItems = document.querySelectorAll('.test-item');
            testItems.forEach(item => {
                item.className = 'test-item pending';
            });
            
            const results = document.querySelectorAll('.result');
            results.forEach(result => {
                result.innerHTML = '';
            });

            document.getElementById('dashboard-data').innerHTML = '';
        }

        // 页面加载完成后显示说明
        window.onload = function() {
            console.log('📋 仪表盘API测试页面已加载');
            console.log('🔧 修复内容: 统一API路径为 /api/v1/...');
        };
    </script>
</body>
</html>
