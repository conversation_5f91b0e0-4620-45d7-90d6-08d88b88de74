<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f7fa;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .status {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-weight: 500;
            text-align: center;
        }
        
        .status.disconnected {
            background: #fef0f0;
            color: #dc2626;
            border: 1px solid #f56c6c;
        }
        
        .status.connecting {
            background: #fff7ed;
            color: #ea580c;
            border: 1px solid #fdba74;
        }
        
        .status.connected {
            background: #f0f9ff;
            color: #059669;
            border: 1px solid #67c23a;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #409eff;
            color: white;
            border: none;
            padding: 10px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            flex: 1;
            min-width: 120px;
        }
        
        .btn:hover {
            background: #337ecc;
        }
        
        .btn:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }
        
        .btn.danger {
            background: #f56c6c;
        }
        
        .btn.danger:hover {
            background: #f04141;
        }
        
        .message-area {
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            background: #fafafa;
            min-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            line-height: 1.5;
        }
        
        .message {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        
        .message:last-child {
            border-bottom: none;
        }
        
        .message.sent {
            color: #409eff;
        }
        
        .message.received {
            color: #67c23a;
        }
        
        .message.error {
            color: #f56c6c;
        }
        
        .message.info {
            color: #909399;
        }
        
        .send-area {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .send-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
        }
        
        .stat-label {
            font-size: 12px;
            color: #909399;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 WebSocket连接测试</h1>
        
        <div id="status" class="status disconnected">
            状态: 未连接
        </div>
        
        <div class="controls">
            <button id="connectBtn" class="btn">连接WebSocket</button>
            <button id="disconnectBtn" class="btn danger" disabled>断开连接</button>
            <button id="clearBtn" class="btn">清空日志</button>
            <button id="testBtn" class="btn" disabled>测试消息</button>
        </div>
        
        <div class="send-area">
            <input type="text" id="messageInput" class="send-input" placeholder="输入要发送的消息..." disabled>
            <button id="sendBtn" class="btn" disabled>发送</button>
        </div>
        
        <div id="messages" class="message-area">
            <div class="message info">等待连接...</div>
        </div>
        
        <div class="stats">
            <div class="stat-card">
                <div id="sentCount" class="stat-value">0</div>
                <div class="stat-label">已发送消息</div>
            </div>
            <div class="stat-card">
                <div id="receivedCount" class="stat-value">0</div>
                <div class="stat-label">已接收消息</div>
            </div>
            <div class="stat-card">
                <div id="connectionTime" class="stat-value">--</div>
                <div class="stat-label">连接时间(秒)</div>
            </div>
            <div class="stat-card">
                <div id="pingTime" class="stat-value">--</div>
                <div class="stat-label">延迟(ms)</div>
            </div>
        </div>
    </div>

    <script>
        class WebSocketTester {
            constructor() {
                this.ws = null;
                this.connectTime = null;
                this.sentCount = 0;
                this.receivedCount = 0;
                this.pingStart = null;
                
                this.initElements();
                this.bindEvents();
            }
            
            initElements() {
                this.statusEl = document.getElementById('status');
                this.connectBtn = document.getElementById('connectBtn');
                this.disconnectBtn = document.getElementById('disconnectBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.testBtn = document.getElementById('testBtn');
                this.messageInput = document.getElementById('messageInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.messagesEl = document.getElementById('messages');
                this.sentCountEl = document.getElementById('sentCount');
                this.receivedCountEl = document.getElementById('receivedCount');
                this.connectionTimeEl = document.getElementById('connectionTime');
                this.pingTimeEl = document.getElementById('pingTime');
            }
            
            bindEvents() {
                this.connectBtn.addEventListener('click', () => this.connect());
                this.disconnectBtn.addEventListener('click', () => this.disconnect());
                this.clearBtn.addEventListener('click', () => this.clearMessages());
                this.testBtn.addEventListener('click', () => this.sendTestMessages());
                this.sendBtn.addEventListener('click', () => this.sendMessage());
                
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.sendMessage();
                    }
                });
            }
            
            connect() {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.addMessage('已经连接', 'info');
                    return;
                }
                
                this.updateStatus('connecting', '正在连接...');
                this.addMessage('正在连接到 WebSocket 服务器...', 'info');
                
                // 连接到后端WebSocket
                const wsUrl = 'ws://localhost:8000/api/v1/ws';
                this.ws = new WebSocket(wsUrl);
                this.connectTime = Date.now();
                
                this.ws.onopen = (event) => {
                    this.updateStatus('connected', '已连接');
                    this.addMessage('WebSocket 连接成功!', 'received');
                    this.enableControls(true);
                    this.startConnectionTimer();
                    
                    // 发送认证消息（如果需要）
                    this.sendAuth();
                };
                
                this.ws.onmessage = (event) => {
                    this.receivedCount++;
                    this.updateStats();
                    
                    try {
                        const data = JSON.parse(event.data);
                        this.handleMessage(data);
                    } catch (e) {
                        this.addMessage(`收到原始消息: ${event.data}`, 'received');
                    }
                };
                
                this.ws.onclose = (event) => {
                    this.updateStatus('disconnected', '连接已断开');
                    this.addMessage(`连接断开: ${event.code} - ${event.reason}`, 'error');
                    this.enableControls(false);
                    this.stopConnectionTimer();
                };
                
                this.ws.onerror = (error) => {
                    this.updateStatus('disconnected', '连接错误');
                    this.addMessage('WebSocket 连接错误', 'error');
                    this.enableControls(false);
                };
            }
            
            disconnect() {
                if (this.ws) {
                    this.ws.close(1000, 'User requested disconnect');
                    this.ws = null;
                }
            }
            
            sendAuth() {
                // 发送认证消息
                const authMessage = {
                    type: 'auth',
                    token: 'test-token', // 在实际应用中应该是真实的token
                    timestamp: Date.now()
                };
                this.send(authMessage);
            }
            
            send(data) {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    const message = JSON.stringify(data);
                    this.ws.send(message);
                    this.sentCount++;
                    this.updateStats();
                    this.addMessage(`发送: ${message}`, 'sent');
                    
                    // 记录ping时间
                    if (data.type === 'ping') {
                        this.pingStart = Date.now();
                    }
                } else {
                    this.addMessage('WebSocket 未连接', 'error');
                }
            }
            
            sendMessage() {
                const message = this.messageInput.value.trim();
                if (message) {
                    try {
                        // 尝试解析为JSON
                        const data = JSON.parse(message);
                        this.send(data);
                    } catch (e) {
                        // 发送纯文本消息
                        this.send({
                            type: 'message',
                            content: message,
                            timestamp: Date.now()
                        });
                    }
                    this.messageInput.value = '';
                }
            }
            
            sendTestMessages() {
                const testMessages = [
                    { type: 'ping', timestamp: Date.now() },
                    { type: 'subscribe', topic: 'market.quotes' },
                    { type: 'subscribe', topic: 'market.ticks' },
                    { type: 'get_market_data', symbols: ['000001.SZ', '600000.SH'] }
                ];
                
                testMessages.forEach((msg, index) => {
                    setTimeout(() => {
                        this.send(msg);
                    }, index * 500);
                });
            }
            
            handleMessage(data) {
                const timestamp = new Date().toLocaleTimeString();
                let messageText = `[${timestamp}] `;
                
                switch (data.type) {
                    case 'pong':
                        if (this.pingStart) {
                            const latency = Date.now() - this.pingStart;
                            this.pingTimeEl.textContent = latency;
                            messageText += `Pong received (延迟: ${latency}ms)`;
                        } else {
                            messageText += 'Pong received';
                        }
                        break;
                    case 'connection_established':
                        messageText += '连接建立确认';
                        break;
                    case 'subscription_confirmed':
                        messageText += `订阅确认: ${data.topic}`;
                        break;
                    case 'market_data':
                        messageText += `市场数据: ${JSON.stringify(data.data)}`;
                        break;
                    case 'heartbeat':
                        messageText += '心跳';
                        return; // 不显示心跳消息
                    default:
                        messageText += JSON.stringify(data);
                }
                
                this.addMessage(messageText, 'received');
            }
            
            addMessage(text, type = 'info') {
                const messageEl = document.createElement('div');
                messageEl.className = `message ${type}`;
                messageEl.textContent = text;
                
                this.messagesEl.appendChild(messageEl);
                this.messagesEl.scrollTop = this.messagesEl.scrollHeight;
            }
            
            clearMessages() {
                this.messagesEl.innerHTML = '<div class="message info">日志已清空</div>';
            }
            
            updateStatus(status, text) {
                this.statusEl.className = `status ${status}`;
                this.statusEl.textContent = `状态: ${text}`;
            }
            
            enableControls(enabled) {
                this.connectBtn.disabled = enabled;
                this.disconnectBtn.disabled = !enabled;
                this.testBtn.disabled = !enabled;
                this.sendBtn.disabled = !enabled;
                this.messageInput.disabled = !enabled;
            }
            
            updateStats() {
                this.sentCountEl.textContent = this.sentCount;
                this.receivedCountEl.textContent = this.receivedCount;
            }
            
            startConnectionTimer() {
                this.connectionTimer = setInterval(() => {
                    if (this.connectTime) {
                        const seconds = Math.floor((Date.now() - this.connectTime) / 1000);
                        this.connectionTimeEl.textContent = seconds;
                    }
                }, 1000);
            }
            
            stopConnectionTimer() {
                if (this.connectionTimer) {
                    clearInterval(this.connectionTimer);
                    this.connectionTimer = null;
                }
                this.connectionTimeEl.textContent = '--';
                this.pingTimeEl.textContent = '--';
            }
        }
        
        // 初始化测试器
        const tester = new WebSocketTester();
        
        // 页面加载时的提示
        tester.addMessage('WebSocket测试器已就绪', 'info');
        tester.addMessage('点击"连接WebSocket"开始测试', 'info');
        tester.addMessage('服务器地址: ws://localhost:8000/api/v1/ws', 'info');
    </script>
</body>
</html>